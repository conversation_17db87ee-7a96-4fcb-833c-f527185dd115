# AlphaGrep Invoice Processor Environment Variables
# ----------------------------------------------
# Instructions:
# 1. Copy this file to .env in the root directory
# 2. Replace the placeholder values with your actual credentials
# 3. Keep the .env file secure and never commit it to version control
# 4. Make sure python-dotenv is installed: pip install python-dotenv

# Azure Computer Vision OCR Credentials
# Required for processing complex invoices (ALPHAGREP type)
AZURE_ENDPOINT=https://your-azure-endpoint.cognitiveservices.azure.com/
AZURE_SUBSCRIPTION_KEY=your_azure_subscription_key

# Google Cloud Vision OCR Credentials
# Option 1: Set path to credentials JSON file
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/google-credentials.json
# Option 2: Use service account key directly (alternative to file path)
# GOOGLE_CLOUD_PROJECT=your-project-id
# GOOGLE_CLOUD_CREDENTIALS_JSON={"type":"service_account","project_id":"..."}

# LLM API Credentials
# OpenAI (for GPT models)
OPENAI_API_KEY=your_openai_api_key

# Anthropic (for Claude models)
ANTHROPIC_API_KEY=your_anthropic_api_key

# Google AI (for Gemini models)
# Usually uses the same GOOGLE_APPLICATION_CREDENTIALS as above

# Application Settings
# Optional: Customize these settings as needed
# LOG_LEVEL=INFO
# OUTPUT_DIRECTORY=./output
# CHART_OF_ACCOUNTS_PATH=./src/assets/CoA.xlsx

# Supported file types for invoice processing (comma-separated)
SUPPORTED_FILE_TYPES=pdf,png,jpg,jpeg,tiff,tif
