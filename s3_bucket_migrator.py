import boto3

s3 = boto3.client('s3')

source_bucket = 'pikernel-debug-results'
destination_bucket = 'pikernel-debug-archive'
prefix = ''  # Optional: Set to a subfolder like 'folder1/' if needed

paginator = s3.get_paginator('list_objects_v2')
pages = paginator.paginate(Bucket=source_bucket, Prefix=prefix)

for page in pages:
    for obj in page.get('Contents', []):
        source_key = obj['Key']
        copy_source = {'Bucket': source_bucket, 'Key': source_key}

        # Copy to destination
        s3.copy(copy_source, destination_bucket, source_key)
        print(f"Copied: {source_key}")

        # Delete from source
        s3.delete_object(Bucket=source_bucket, Key=source_key)
        print(f"Deleted: {source_key}")
