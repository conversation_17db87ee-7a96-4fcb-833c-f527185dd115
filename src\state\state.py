from pydantic import BaseModel, <PERSON>
from typing import Dict, List, Any

class ProcessState(BaseModel):
    input_folder: str = ""
    output_folder: str = ""
    filename: str = ""
    invoice_detector: str = "machine_learning"
    invoices: Dict[str, str] = Field(default_factory=dict)
    ocr_results: Dict[str, str] = Field(default_factory=dict)
    ocr_confidences: Dict[str, Dict[str, float]] = Field(default_factory=dict)
    selected_ocr_provider: str = ""
    selected_ocr_text: str = ""
    textract_recursion_counter: int = 0
    parsed_jsons: Dict[str, str] = Field(default_factory=dict)
    parsed_data: dict = Field(default_factory=dict)  # <-- NEW FIELD to hold enriched data
    llm_provider: str = "openai"  # default LLM provider
    llm_model: str = "gpt-4o"     # default model name
    accounting_package: str = "quickbooks"
    invoice_type: str = "UNKNOWN"
    is_invoice: bool = False  # <-- NEW FIELD to hold invoice detection result
    bill_posted: bool = False
    intent_memos: List[Any] = Field(default_factory=list) 
    project_code_memos: List[Any] = Field(default_factory=list)

    def update_invoice(self, filename: str, status: str):
        self.invoices[filename] = status

    def __str__(self):
        return self.model_dump_json(indent=2)

    class Config:
        # Allow extra keys to be included if needed.
        extra = "allow"
