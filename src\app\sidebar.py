import streamlit as st
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def render_sidebar():
    """
    Renders the application sidebar with configuration options.

    Returns:
        tuple: (provider, model_name, uploaded_files, process_button)
    """
    st.sidebar.title("Pillar PiKernel Invoice Processor")

    # Get supported file types from environment or use defaults
    supported_types_str = os.getenv("SUPPORTED_FILE_TYPES", "pdf,png,jpg,jpeg,tiff,tif")
    supported_types = [ext.strip() for ext in supported_types_str.lower().split(",")]

    # LLM Provider Selection
    provider = st.sidebar.selectbox(
        "Select LLM Provider",
        ["OpenAI", "Anthropic", "Google"],
        index=0  # Default to OpenAI
    )

    # Model Selection based on provider
    if provider == "OpenAI":
        model_name = st.sidebar.selectbox(
            "Select Model",
            ["gpt-4o", "gpt-3.5-turbo", "gpt-4"],
            index=0
        )
    elif provider == "Anthropic":
        model_name = st.sidebar.selectbox(
            "Select Model",
            ["claude-3-opus", "claude-3-sonnet", "claude-3-haiku"]
        )
    else:  # Google
        model_name = st.sidebar.selectbox(
            "Select Model",
            ["gemini-pro", "gemini-ultra"]
        )

    # File Upload with dynamic file types
    uploaded_files = st.sidebar.file_uploader(
        "Upload Invoice Files",
        type=supported_types,
        accept_multiple_files=True
    )

    # Process Button
    process_button = st.sidebar.button("Process Invoice(s)")

    # Information section
    st.sidebar.markdown("---")
    st.sidebar.info(
        "This application processes invoices using OCR and LLMs to extract "
        "structured data, classify expenses, and provide visualizations."
    )

    return provider, model_name, uploaded_files, process_button
