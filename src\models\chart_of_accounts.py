# src/models/chart_of_accounts.py
from abc import ABC, abstractmethod
import pandas as pd
import os
import colorsys
from typing import Dict, Set, Any


class ChartOfAccounts(ABC):
    """
    Abstract base class for Chart of Accounts functionality.
    Defines the interface for different CoA implementations.
    """

    @abstractmethod
    def get_unique_subtypes(self) -> Set[str]:
        """Returns a set of unique sub-account types"""
        pass

    @abstractmethod
    def get_account_mapping(self) -> Dict[str, Dict]:
        """
        Returns a dictionary mapping account names to their details
        
        Returns:
            Dict[str, Dict]: A dictionary where the key is the FullyQualifiedName and the value is a dictionary containing:
                - Name: The account name
                - AccountType: The type of account
                - AccountSubType: The sub-type of account
                - Id: The account ID
                - CurrencyRef.value: The currency code
        """
        pass

    def generate_colors(self) -> Dict[str, str]:
        """
        Generates and returns a dictionary matching sub-account types to colors.
        The colors are presented as hexadecimal strings, as used in HTML and CSS.
        """
        unique_subtypes = list(self.get_unique_subtypes())
        n = len(unique_subtypes)
        hsv_tuples = [(x * 1.0 / n, 0.5, 0.9) for x in range(n)]
        rgb_tuples = [colorsys.hsv_to_rgb(*x) for x in hsv_tuples]
        color_dict = {}
        for subtype, (r, g, b) in zip(unique_subtypes, rgb_tuples):
            color_dict[subtype] = '#{:02x}{:02x}{:02x}'.format(int(r * 255), int(g * 255), int(b * 255))
        print(f"[ChartOfAccounts] Generated colors for {len(color_dict)} subtypes")
        return color_dict

    @staticmethod
    def create(coa_type: str = "Excel", file_path: str = None) -> 'ChartOfAccounts':
        """
        Factory method to create appropriate Chart of Accounts implementation.

        Args:
            coa_type (str): Type of Chart of Accounts implementation to create
            file_path (str, optional): Path to the CoA file

        Returns:
            ChartOfAccounts: An implementation of the ChartOfAccounts class

        Raises:
            ValueError: If an invalid CoA type is specified
        """
        if coa_type == "Excel":
            return ExcelChartOfAccounts(file_path)
        else:
            raise ValueError(f"Invalid CoA type: {coa_type}")


class ExcelChartOfAccounts(ChartOfAccounts):
    """
    Implementation of Chart of Accounts that loads data from an Excel file.
    """

    def __init__(self, file_path: str = None):
        """
        Initialize with path to Excel file containing Chart of Accounts data.

        Args:
            file_path (str, optional): Path to the Excel file. If None, defaults to 'CoA.xlsx'
                                        in the project's assets directory.
        """
        if file_path is None:
            # Default to the assets directory in the project
            base_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'assets')
            file_path = os.path.join(base_path, 'CoA.xlsx')

        print(f"[ExcelChartOfAccounts] Loading Chart of Accounts from {file_path}")

        try:
            self.df = pd.read_excel(file_path)
            print(f"[ExcelChartOfAccounts] Successfully loaded Excel file with {len(self.df)} rows")
        except Exception as e:
            print(f"[ExcelChartOfAccounts] Error loading Excel file: {e}")
            # Create an empty DataFrame with expected columns
            self.df = pd.DataFrame(columns=['Account type', 'Account subtype', 'Full name'])

        self.expense_accounts = self._process_expense_accounts()
        self.unique_subtypes = self._get_unique_subtypes()
        self.account_mapping = self._create_account_mapping()

    def _process_expense_accounts(self) -> pd.DataFrame:
        #"""Filter accounts to only include expense accounts"""
        #expense_df = self.df[self.df['AccountType'] == 'Expenses'].copy()
        # expand to include Asset AccountType and filter to Active = True
        """Filter accounts to only include expense or asset accounts and active accounts"""
        expense_df = self.df[(self.df['Classification'].isin(['Expense', 'Asset'])) & (self.df['Active'] == True)].copy()
        print(f"[ExcelChartOfAccounts] Found {len(expense_df)} expense accounts")
        return expense_df

    def _get_unique_subtypes(self) -> Set[str]:
        """Get unique sub-account types from expense accounts"""
        subtypes = set(self.expense_accounts['AccountSubType'].unique())
        print(f"[ExcelChartOfAccounts] Found {len(subtypes)} unique subtypes")
        return subtypes

    def _create_account_mapping(self) -> Dict[str, Dict]:
        """Create mapping from full account names to account details"""
        mapping = {}
        for _, row in self.expense_accounts.iterrows():
            mapping[row['FullyQualifiedName']] = {
                'Name': row['Name'],
                'AccountType': row['AccountType'],
                'AccountSubType': row['AccountSubType'],
                'Id': row['Id'],
                'CurrencyRef.value': row['CurrencyRef.value']
            }
        print(f"[ExcelChartOfAccounts] Created mapping for {len(mapping)} accounts")
        return mapping

    def get_unique_subtypes(self) -> Set[str]:
        """Implementation of abstract method to get unique subtypes"""
        return self.unique_subtypes

    def get_account_mapping(self) -> Dict[str, Dict]:
        """Implementation of abstract method to get account mapping"""
        return self.account_mapping
