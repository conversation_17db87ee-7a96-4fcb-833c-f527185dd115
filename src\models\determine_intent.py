# src/models/determine_intent.py
from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
import logging


class IntentParser:
    def __init__(self, llm, chart_of_accounts, memos):
        self.llm = llm
        self.output_parser = StrOutputParser()
        self.memos = memos
        self.prompt = self._create_prompt(chart_of_accounts)
        self.chain = self.prompt | self.llm | self.output_parser

    def _create_prompt(self, chart_of_accounts):
        unique_subtypes = chart_of_accounts.get_unique_subtypes()
        account_mapping = chart_of_accounts.get_account_mapping()

        # List of valid account names only
        account_names = "\n".join(account_mapping.keys())
        memos_str = "\n\n".join([f"Memo: {memo.text}" for memo in self.memos])

        print(memos_str)

        return ChatPromptTemplate.from_messages([
            ("system", f'''You are an expert at determining which account an invoice line item should be posted to based on its
            description. Here are all the valid fully qualified account names:

            {account_names}
            
            Here are specific accounting instructions for this organization to help you decide where this firm posts 
            certain line items:  

            {memos_str}

            Instructions:
            1. Use your skill and judgement to determine the correct account from the description provided
            2. Only return a fully qualified account name from the provided list
            3. Only return the account name, no other data or text

            Extract the information and format it according to this schema'''),
            ("human", "Line item description: {text}\n\n"
                      "Determine the best account for this line item in an invoice.")
        ])

    def determine_intent(self, text: str):
        logging.debug(f"Processing invoice text: {text[:100]}...")
        if not text or text.isspace():
            raise ValueError("Invalid input. Please provide the invoice text.")
        try:
            result = self.chain.invoke({"text": text})
            logging.debug(f"Parsing result: {result}")
            return result
        except Exception as e:
            logging.error(f"Error processing invoice: {str(e)}")
            raise ValueError(f"Error processing invoice: {str(e)}")
