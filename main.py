#!/usr/bin/env python3
# to run use - python -m streamlit run main.py
import sys
import os

# Debug print: show which Python interpreter is running
print("Using Python interpreter:", sys.executable)

# Ensure the project root is in sys.path so that "src" can be found.
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
    print("Added project root to sys.path:", project_root)

# Import the main function from our Streamlit app.
from src.app.streamlit_app import main

if __name__ == "__main__":
    main()
