# File: src/views/invoice_view.py
import streamlit as st
import pandas as pd
from typing import Dict, Any


class FormattedInvoiceView:
    """
    View component for displaying formatted invoices with color-coded line items.
    Follows the MVC pattern as a View component.
    """

    def __init__(self, invoice_data: Dict[str, Any], sub_account_colors: Dict[str, str]):
        """
        Initialize with the invoice data and sub-account color mapping.
        """
        self.invoice_data = invoice_data
        self.sub_account_colors = sub_account_colors

    def render(self):

        self._render_header()
        col1, col2 = st.columns(2)
        with col1:
            self._render_supplier()
        with col2:
            self._render_customer()
        self._render_line_items()

    def _render_header(self):
        header = self.invoice_data.get('invoice_header', {})
        st.markdown(f"### Invoice #{header.get('invoice_number', 'Unknown')}")
        st.markdown(
            f"**Date:** {header.get('invoice_date', 'Unknown')} | **Due Date:** {header.get('due_date', 'Unknown')}")
        st.markdown(
            f"**Total Amount:** {header.get('currency', '$')} {self._format_currency(header.get('total_amount', 0))}")

    def _render_supplier(self):
        supplier = self.invoice_data.get('invoice_header', {}).get('supplier', {}) or {}
        st.subheader("Supplier")
        st.write(f"**Name:** {supplier.get('name', 'Unknown')}")
        address = supplier.get('address', {})
        if address.get('street'):
            st.write(f"**Address:** {address.get('street')}")
            st.write(f"{address.get('city', '')}, {address.get('state', '')} {address.get('zip_code', '')}")
            if address.get('country'):
                st.write(f"{address.get('country')}")
        contact = supplier.get('contact', {})
        if any(contact.get(field) for field in ['name', 'email', 'phone']):
            st.write("**Contact Information:**")
            if contact.get('name'):
                st.write(f"Contact: {contact.get('name')}")
            if contact.get('email'):
                st.write(f"Email: {contact.get('email')}")
            if contact.get('phone'):
                st.write(f"Phone: {contact.get('phone')}")

    def _render_customer(self):
        customer = self.invoice_data.get('invoice_header', {}).get('customer', {})
        st.subheader("Customer")
        st.write(f"**Name:** {customer.get('name', 'Unknown')}")
        address = customer.get('address', {})
        if address.get('street'):
            st.write(f"**Address:** {address.get('street')}")
            st.write(f"{address.get('city', '')}, {address.get('state', '')} {address.get('zip_code', '')}")
            if address.get('country'):
                st.write(f"{address.get('country')}")
        contact = customer.get('contact', {})
        if any(contact.get(field) for field in ['name', 'email', 'phone']):
            st.write("**Contact Information:**")
            if contact.get('name'):
                st.write(f"Contact: {contact.get('name')}")
            if contact.get('email'):
                st.write(f"Email: {contact.get('email')}")
            if contact.get('phone'):
                st.write(f"Phone: {contact.get('phone')}")

    def _render_line_items(self):
        """Render the line items table with color-coding based on sub-account."""
        # First, get line_items from the invoice_data
        line_items = self.invoice_data.get('line_items', [])

        # Handle different data structures that might be present
        if isinstance(line_items, dict):
            # If it's a dictionary (e.g., {"0": {...}, "1": {...}}), convert to list
            line_items = list(line_items.values())
        elif isinstance(line_items, str):
            # If it's a JSON string, parse it
            try:
                import json
                line_items = json.loads(line_items)
                if isinstance(line_items, dict) and "items" in line_items:
                    line_items = line_items["items"]
            except Exception as e:
                st.error(f"Error parsing line items JSON: {str(e)}")
                return

        if not line_items:
            st.warning("No line items found in this invoice.")
            return

        st.subheader("Line Items")

        # Create a clean list of dictionaries for the DataFrame
        cleaned_items = []
        for item in line_items:
            if isinstance(item, dict):
                # Include all the enriched fields we need
                cleaned_item = {
                    'description': item.get('description', ''),
                    'quantity': item.get('quantity', 0),
                    'unit_price': item.get('unit_price', 0),
                    'total_price': item.get('total_price', 0),
                    'sub_account': item.get('sub_account', 'Uncategorized'),
                    'account_id': item.get('account_id', ''),
                    'currency': item.get('currency', 'USD'),  # Add currency field
                    'project_codes': item.get('project_codes', []),
                    'employee_codes': item.get('employee_codes', []),
                    'department_codes': item.get('department_codes', []),
                    'frequency': item.get('frequency', 'MONTHLY')
                }
                cleaned_items.append(cleaned_item)

        try:
            # Create DataFrame from the cleaned items
            df = pd.DataFrame(cleaned_items)

            # Select and order columns for display
            columns_to_display = ['description', 'quantity', 'unit_price', 'total_price', 'currency', 'sub_account', 
                                'account_id', 'project_codes', 'employee_codes', 'department_codes', 'frequency']
            display_df = df[[col for col in columns_to_display if col in df.columns]]

            # Rename columns for better display
            display_df = display_df.rename(columns={
                'description': 'Description',
                'quantity': 'Qty',
                'unit_price': 'Unit Price',
                'total_price': 'Total Price',
                'currency': 'Currency',  # Add currency column rename
                'sub_account': 'Category',
                'account_id': 'Category ID',
                'project_codes': 'Project Codes',
                'employee_codes': 'Employee Codes',
                'department_codes': 'Department Codes',
                'frequency': 'Frequency'
            })

            # Format numeric columns if present
            if 'Qty' in display_df.columns:
                display_df['Qty'] = display_df['Qty'].apply(self._format_quantity)
            if 'Unit Price' in display_df.columns:
                display_df['Unit Price'] = display_df['Unit Price'].apply(self._format_currency)
            if 'Total Price' in display_df.columns:
                display_df['Total Price'] = display_df['Total Price'].apply(self._format_currency)

            # turn lists into strings for display
            for col in ['Project Codes', 'Employee Codes', 'Department Codes']:
                if col in display_df.columns:
                    display_df[col] = display_df[col].apply(lambda x: ', '.join(x) if isinstance(x, list) else x)

            # Apply row color styling based on sub-account
            styled_df = display_df.style.apply(self._color_rows, axis=1)
            st.dataframe(styled_df)

            # Display a summary of the totals grouped by currency
            if 'Total Price' in display_df.columns and 'Currency' in display_df.columns:
                totals_by_currency = display_df.groupby('Currency').agg({'Total Price': 'sum'})
                for currency, total in totals_by_currency.iterrows():
                    st.markdown(f"**Total Amount ({currency}): ${self._format_currency(total['Total Price'])}**")

            # Render the color key for sub-accounts
            self._render_color_key()

        except Exception as e:
            st.error(f"Error creating line items table: {str(e)}")

    def _color_rows(self, row):
        """Apply a background color to a row based on the sub-account/category."""
        # Try different possible column names that might contain the sub-account
        for column_name in ['Category', 'sub_account']:
            if column_name in row:
                sub_account = row[column_name]
                color = self.sub_account_colors.get(sub_account, '#FFFFFF')

                # Calculate text color for better contrast
                # Dark background needs white text, light background needs black text
                r, g, b = int(color[1:3], 16), int(color[3:5], 16), int(color[5:7], 16)
                brightness = (r * 299 + g * 587 + b * 114) / 1000
                text_color = 'white' if brightness < 128 else 'black'

                return [f'background-color: {color}; color: {text_color};'] * len(row)

        # Default styling if no category column is found
        return [''] * len(row)

    def _format_currency(self, value):
        try:
            return f"{float(value):,.2f}"
        except (ValueError, TypeError):
            return str(value)

    def _format_quantity(self, value):
        try:
            return f"{int(value)}"
        except (ValueError, TypeError):
            return str(value)

    def _render_color_key(self):
        """Render a color key for sub-accounts."""
        st.subheader("Sub-Account Color Key")
        col_count = 3  # Number of columns for the key
        cols = st.columns(col_count)
        for idx, (sub_account, color) in enumerate(self.sub_account_colors.items()):
            with cols[idx % col_count]:
                st.markdown(
                    f'<div style="display: flex; align-items: center; margin-bottom: 5px;">'
                    f'<div style="width: 20px; height: 20px; background-color: {color}; margin-right: 10px;"></div>'
                    f'<span>{sub_account}</span></div>',
                    unsafe_allow_html=True
                )
