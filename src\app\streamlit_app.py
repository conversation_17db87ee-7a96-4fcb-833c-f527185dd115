import streamlit as st
import sys
import os


from src.app.sidebar import render_sidebar
from src.app.components.tabs import render_tabs
from src.controllers.invoice_controller_graph import InvoiceController
from src.models.chart_of_accounts import ChartOfAccounts
from src.models.accounting_memos import MemoRepository
from src.models.ledger import LedgerRepository
from src.views.invoice_view import FormattedInvoiceView
from src.views.results_view import ResultsView
from src.views.workflow_view import WorkflowView
from src.views.sunburst_view import SunburstView
from src.views.network_view import NetworkView
#from src.views.schedule_view import ScheduleView
from src.views.schedule_view import Prepayments_Schedule, Accruals_Schedule
from src.views.bi_view import BusinessIntelligenceView

# Rest of the file remains the same...

def extract_parsed_data(invoice_states):
    """
    Helper function to extract parsed_data from invoice states consistently.

    Args:
        invoice_states: Dictionary or list of invoice states

    Returns:
        List of dictionaries with parsed_data
    """
    processed_data = []

    # Handle both dictionary and list inputs
    if isinstance(invoice_states, dict):
        states_to_process = invoice_states.values()
    else:
        states_to_process = invoice_states

    for state in states_to_process:
        # First check if we have a dict with parsed_data
        if isinstance(state, dict) and "parsed_data" in state and state["parsed_data"]:
            processed_data.append({"parsed_data": state["parsed_data"]})

        # If state is a Pydantic model, try to access parsed_data as an attribute
        elif hasattr(state, "parsed_data") and state.parsed_data:
            processed_data.append({"parsed_data": state.parsed_data})

        # Legacy fallback - look for data in the old location
        elif isinstance(state, dict) and "parsed_jsons" in state:
            # Try to get the first JSON in parsed_jsons
            first_json_key = next(iter(state["parsed_jsons"]), None)
            if first_json_key:
                try:
                    import json
                    parsed_json = json.loads(state["parsed_jsons"][first_json_key])
                    processed_data.append({"parsed_data": parsed_json})
                except Exception:
                    # Skip this entry if we can't parse it
                    pass

    return processed_data

def main():
    """
    Main Streamlit application entry point.

    This function initializes the UI components, manages the overall flow,
    and connects the UI with the controller.
    """
    st.set_page_config(page_title="Pikernel Invoice Processor", layout="wide")

    # Initialize memo repositories
    intent_memo_repository = MemoRepository(os.path.join("data", "intent_memos.pkl"))
    code_memo_repository = MemoRepository(os.path.join("data", "code_memos.pkl"))

    # Initialize other components
    ledger = LedgerRepository()
    controller = InvoiceController(".", ".")
    chart_of_accounts = ChartOfAccounts.create("Excel")
    sub_account_colors = chart_of_accounts.generate_colors()

    # Initialize session state for storing results
    if 'processing_results' not in st.session_state:
        st.session_state.processing_results = {}
    if 'workflow_graph' not in st.session_state:
        st.session_state.workflow_graph = None

    # Render sidebar and get user inputs
    provider, model_name, uploaded_files, process_button = render_sidebar()

    # Render tabs
    process_tab, results_tab, workflow_tab, formatted_tab, network_tab, schedule_tab, bi_tab, instructions_tab = render_tabs()

    # Handle processing when button is clicked
    with process_tab:
        st.header("Process Invoices")
        if process_button:
            if not uploaded_files:
                st.warning("Please upload invoice PDF files before processing.")
            else:
                with st.spinner("Processing invoices..."):
                    try:
                        # Process invoices and get results
                        results = controller.process_invoices(uploaded_files, provider, model_name, ledger, intent_memo_repository.read_all_memos(), code_memo_repository.read_all_memos())

                        # Store results in session state
                        st.session_state.processing_results = results

                        # Store workflow graph
                        if hasattr(controller, 'workflow') and controller.workflow:
                            st.session_state.workflow_graph = controller.workflow

                        st.success(f"Successfully processed {len(uploaded_files)} invoice(s).")
                    except Exception as e:
                        st.error(f"An error occurred during processing: {str(e)}")
                        import traceback
                        st.text(traceback.format_exc())

    # Render results view
    with results_tab:
        st.header("OCR Results")
        if st.session_state.processing_results:
            for filename, state in st.session_state.processing_results.items():
                with st.expander(f"Results for {filename}"):
                    ResultsView(state).render()
        else:
            st.info("OCR results will be displayed here after processing.")

    # Render workflow view
    with workflow_tab:
        st.header("Workflow Graph")
        WorkflowView(st.session_state.workflow_graph).render()

    # Render formatted invoices view
    with formatted_tab:
        st.header("Formatted Invoices")
        if st.session_state.processing_results:
            for filename, invoice_state in st.session_state.processing_results.items():
                with st.expander(f"Invoice: {filename}"):
                    # First check if we have a dict with parsed_data
                    if isinstance(invoice_state, dict) and "parsed_data" in invoice_state and invoice_state[
                        "parsed_data"]:
                        FormattedInvoiceView(invoice_state["parsed_data"], sub_account_colors).render()

                    # If invoice_state is a Pydantic model, try to access parsed_data as an attribute
                    elif hasattr(invoice_state, "parsed_data") and invoice_state.parsed_data:
                        FormattedInvoiceView(invoice_state.parsed_data, sub_account_colors).render()

                    # Legacy fallback - look for data in the old location
                    elif isinstance(invoice_state, dict) and "parsed_jsons" in invoice_state:
                        # Try to get the first JSON in parsed_jsons
                        first_json_key = next(iter(invoice_state["parsed_jsons"]), None)
                        if first_json_key:
                            try:
                                import json
                                parsed_json = json.loads(invoice_state["parsed_jsons"][first_json_key])
                                FormattedInvoiceView(parsed_json, sub_account_colors).render()
                            except Exception as e:
                                st.warning(f"Could not parse JSON data: {str(e)}")
                    else:
                        st.warning(f"No parsed data available for {filename}")
        else:
            st.info("Formatted invoices will be displayed here after processing.")

    # Render network view
    with network_tab:
        st.header("Account Relationships")
        if st.session_state.processing_results:
            processed_data = extract_parsed_data(st.session_state.processing_results)
            if processed_data:
                NetworkView(processed_data, sub_account_colors).render()
            else:
                st.info("No parsed data available for visualization.")
        else:
            st.info("Account relationships will be displayed here after processing.")

    # Render schedule view
    with schedule_tab:
        st.header("Schedules")
        if st.session_state.processing_results:
            processed_data = extract_parsed_data(st.session_state.processing_results)
            if processed_data:
                #ScheduleView(processed_data).render()
                Prepayments_Schedule(processed_data).render()
                Accruals_Schedule(processed_data).render()
            else:
                st.info("No parsed data available for visualization.")
        else:
            st.info("Schedules will be displayed here after processing.")

    # Render BI view
    with bi_tab:
        st.header("Business Intelligence")
        if st.session_state.processing_results:
            processed_data = extract_parsed_data(st.session_state.processing_results)
            if processed_data:
                BusinessIntelligenceView(processed_data, sub_account_colors, ledger.read_all()).render()
            else:
                st.info("No parsed data available for visualization.")
        else:
            st.info("Business intelligence will be displayed here after processing.")

    # Render firm-specific instructions view
    with instructions_tab:
        st.header("Firm-specific Instructions")

        # Split the interface into two columns
        col1, col2 = st.columns(2)

        # Intent Memos Interface
        with col1:
            st.subheader("Expense Categories (Intent Memos)")
            for i, memo in enumerate(intent_memo_repository.read_all_memos()):
                with st.expander(f"Memo {i+1} - Created: {memo.created_date.strftime('%Y-%m-%d %H:%M:%S')} "
                                 f"- Modified: {memo.modified_date.strftime('%Y-%m-%d %H:%M:%S')}"):
                    memo_text = st.text_area("Edit memo", memo.text, key=f"intent_memo_{i}")
                    col1a, col1b = st.columns([1, 4])
                    with col1a:
                        if st.button("Save", key=f"save_intent_{i}"):
                            intent_memo_repository.update_memo(i, memo_text)
                            st.rerun()
                    with col1b:
                        if st.button("Delete", key=f"delete_intent_{i}"):
                            intent_memo_repository.delete_memo(i)
                            st.rerun()

            if 'show_new_intent_memo_input' not in st.session_state:
                st.session_state.show_new_intent_memo_input = False

            if 'new_intent_memo_text' not in st.session_state:
                st.session_state.new_intent_memo_text = ""

            if st.button("Add Intent Memo"):
                st.session_state.show_new_intent_memo_input = True

            if st.session_state.get('show_new_intent_memo_input', False):
                st.session_state.new_intent_memo_text = st.text_area("New Intent Memo", st.session_state.new_intent_memo_text, key="new-intent-memo")
                if st.button("Save New Intent Memo"):
                    intent_memo_repository.create_memo(st.session_state.new_intent_memo_text)
                    st.session_state.show_new_intent_memo_input = False
                    st.session_state.new_intent_memo_text = ""
                    st.rerun()

        # Code Memos Interface
        with col2:
            st.subheader("Project, Employee, and Department Codes (Code Memos)")
            for i, memo in enumerate(code_memo_repository.read_all_memos()):
                with st.expander(f"Memo {i+1} - Created: {memo.created_date.strftime('%Y-%m-%d %H:%M:%S')} "
                                 f"- Modified: {memo.modified_date.strftime('%Y-%m-%d %H:%M:%S')}"):
                    memo_text = st.text_area("Edit memo", memo.text, key=f"code_memo_{i}")
                    col2a, col2b = st.columns([1, 4])
                    with col2a:
                        if st.button("Save", key=f"save_code_{i}"):
                            code_memo_repository.update_memo(i, memo_text)
                            st.rerun()
                    with col2b:
                        if st.button("Delete", key=f"delete_code_{i}"):
                            code_memo_repository.delete_memo(i)
                            st.rerun()

            if 'show_new_code_memo_input' not in st.session_state:
                st.session_state.show_new_code_memo_input = False

            if 'new_code_memo_text' not in st.session_state:
                st.session_state.new_code_memo_text = ""

            if st.button("Add Code Memo"):
                st.session_state.show_new_code_memo_input = True

            if st.session_state.get('show_new_code_memo_input', False):
                st.session_state.new_code_memo_text = st.text_area("New Code Memo", st.session_state.new_code_memo_text, key="new-code-memo")
                if st.button("Save New Code Memo"):
                    code_memo_repository.create_memo(st.session_state.new_code_memo_text)
                    st.session_state.show_new_code_memo_input = False
                    st.session_state.new_code_memo_text = ""
                    st.rerun()

if __name__ == "__main__":
    main()
