# Pillar PiKernel Invoice Processor

Pillar PiKernel Invoice Processor is an AI-powered application that automates invoice data extraction, analysis, and visualization. The system uses OCR (Optical Character Recognition) and LLM (Large Language Model) technologies to extract structured data from invoice PDFs and provide insightful visualizations.

## Features

- Automatic extraction of invoice data via OCR
- Intelligent categorization of expenses
- Payment frequency determination
- Multiple visualization approaches for expense analysis
- Supports multiple AI providers (OpenAI, Anthropic, Google)
- Color-coded expense categorization

## Installation

### Prerequisites

- Python 3.10 or later
- pip (Python package manager)
- Git (optional, for cloning the repository)

### Setup Instructions

1. Clone the repository or download the source code:
   ```bash
   git clone https://github.com/yourusername/AlphaGrep.git
   cd AlphaGrep
   ```

2. Create and activate a virtual environment (recommended):
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. Install the required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Set up environment variables:
   - Copy the provided template to create your `.env` file:
     ```bash
     cp env.template .env
     ```
   - Edit the `.env` file with your API keys and credentials

## Running the Application

### From the Command Line

1. Make sure your virtual environment is activated
2. Run the Streamlit application:
   ```bash
   python -m streamlit run main.py
   ```
3. The application should start and open in your default web browser at http://localhost:8501

### Alternative Command Line Options

- Run with specific environment settings:
  ```bash
  OPENAI_API_KEY=your_key_here python -m streamlit run main.py
  ```

- Run with increased logging for debugging:
  ```bash
  python -m streamlit run main.py -- --logger.level=debug
  ```

## Using the Application

### Selecting an AI Provider and Model

1. In the sidebar on the left, select your preferred LLM provider:
   - **OpenAI**: Offers GPT models (GPT-4o, GPT-3.5-turbo, GPT-4)
   - **Anthropic**: Offers Claude models (Claude-3-opus, Claude-3-sonnet, Claude-3-haiku)
   - **Google**: Offers Gemini models (Gemini-pro, Gemini-ultra)

2. After selecting a provider, choose the specific model from the dropdown menu.
   - For best results, we recommend using more capable models like GPT-4o or Claude-3-opus

### Uploading and Processing Invoices

1. In the sidebar, click the "Upload Invoice PDFs" button
2. Select one or more PDF files from your computer
3. Click the "Process Invoice(s)" button to start the extraction process
4. Wait for the processing to complete (this may take 1-2 minutes per invoice)
5. Once processing is complete, explore the results in the various tabs

### Understanding the Tabs

The application has several tabs, each providing different views of the processed invoice data:

#### Process Invoices
- The main tab for uploading and processing invoices
- Shows processing status and completion messages

#### OCR Results
- Displays the raw text extracted from the invoices
- Shows OCR confidence metrics and quality statistics
- Useful for debugging and understanding what the AI models are working with

#### Formatted Invoices
- Presents a clean, structured view of each invoice
- Shows invoice header information, supplier/customer details
- Displays color-coded line items based on expense category
- Includes a legend explaining the category color coding

#### Expense Sunburst
- Interactive sunburst chart visualizing expense distribution
- Hierarchical view from total expenses down to individual line items
- Hover over segments to see detailed expense amounts
- Color-coded by expense category

#### Account Network
- Network graph showing relationships between suppliers and expense categories
- Node size indicates the monetary value
- Visualizes which suppliers contribute to which expense categories
- Provides network metrics such as density and connection counts

#### Payment Schedule
- Displays recurring payment information
- Filtered by payment frequency (Annual, Semi-Annual, Quarterly, Monthly)
- Generates payment projections for future periods
- Useful for financial planning and cash flow management

#### Business Intelligence
- Comprehensive analytics dashboard for expense data
- Time series analysis showing expense trends
- Category analysis with charts and breakdowns
- Supplier analysis showing top vendors and spending patterns

## Troubleshooting

### Common Issues

1. **OCR Error**: If Azure OCR fails, the system will fall back to Google Vision OCR. Ensure your Azure credentials are correctly set in the `.env` file.

2. **Missing Line Items**: For invoices with many line items, sometimes the AI may not capture all items. Try using more powerful models like GPT-4o or Claude-3-opus.

3. **Invalid JSON Error**: Occasionally, the AI may produce invalid JSON. The application includes automatic repair mechanisms, but some complex invoices might still cause issues.

4. **Dependencies**: If you encounter module import errors, ensure you've installed all required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

### Getting Support

If you encounter issues not covered in this guide, please:
1. Check the console output for error messages
2. Verify your API credentials and environment settings
3. Open an issue in the GitHub repository with a detailed description of the problem

## License

This project is released under the MIT License. See the LICENSE file for details.

## Acknowledgments

- The project uses several open-source libraries and AI services
- Chart of Accounts structure based on standard accounting practices
- Special thanks to all contributors and testers

---

*Note: This application is for demonstration and educational purposes. For production use, additional security measures and optimizations should be implemented.*

# Handy Debugging Tips

This document provides useful debugging commands and techniques for the AlphaGrep Invoice Processor application.

## Using Grep for Log Analysis

The application produces detailed logs that can be analyzed using `grep` to identify specific issues or track the processing flow. Here are some useful grep commands:

### Tracking Processing Pipeline Stages

```bash
# Track an invoice through the entire processing pipeline
grep -i "invoice.*processing" log_output.txt

# Track specific OCR processing
grep -i "extracting text" log_output.txt

# See all LLM interactions 
grep -i "\[llm_" log_output.txt

# Check which nodes were executed in the processing graph
grep -i "\[.*_node\]" log_output.txt
```

### Identifying Errors

```bash
# Find all errors
grep -i "error" log_output.txt

# Find JSON parsing errors
grep -i "json.*error\|parser error" log_output.txt

# Check for OCR recognition issues
grep -i "no text detected\|low confidence" log_output.txt

# Find token/API limit errors
grep -i "token\|limit\|quota" log_output.txt
```

### Monitoring Data Extraction

```bash
# Track line item extraction
grep -i "line item\|extracted.*line" log_output.txt

# Check number of line items extracted
grep -i "number of line items\|successfully extracted" log_output.txt

# Track intent and frequency determination 
grep -E "STARTING (INTENT|FREQUENCY) DETERMINATION" log_output.txt
grep -E "Determined (sub-account|frequency)" log_output.txt
```

### Looking at State Changes

```bash
# See the invoice classification
grep -i "invoice classified as" log_output.txt

# Track routing decisions
grep -i "routing to\|route_by" log_output.txt

# Check which OCR provider was selected
grep -i "selected.*ocr" log_output.txt

# Verification of parsed data updates
grep -i "updated state\[" log_output.txt
```

## Using Console Printing for Specific Values

Add these debug print statements in the code to help diagnose issues:

```python
# Print state dictionary keys
print(f"[DEBUG] State keys: {list(state.keys())}")

# Print data structure sizes
print(f"[DEBUG] Line items length: {len(state.get('parsed_data', {}).get('line_items', []))}")

# Print memory usage
import sys
print(f"[DEBUG] State size: {sys.getsizeof(state)} bytes")
```

## Examining JSON Data Files

The application generates JSON files that can be examined for debugging:

```bash
# Find all output JSON files
find . -name "*_enriched.json" | sort

# Count items in a JSON file
jq '.line_items | length' output_file.json

# Check specific field extraction
jq '.invoice_header.invoice_number' output_file.json
```

## Debugging OCR Issues

If OCR is failing, check these specific conditions:

```bash
# Check if credentials were properly loaded
grep -i "credentials\|using endpoint\|using key" log_output.txt

# Check image conversion for PDFs
grep -i "convert.*pdf\|converted pdf" log_output.txt

# Look for OCR confidence scores
grep -i "confidence.*:" log_output.txt
```

## LLM Processing Issues

If the LLM is not extracting data correctly:

```bash
# Check the prompt being sent
grep -i "sending prompt with" log_output.txt

# Verify model selection
grep -i "creating llm with model" log_output.txt

# Inspect JSON validation errors
grep -i "validation error\|failed to validate" log_output.txt
```

## Using the llm_raw_response.txt File

The application saves raw LLM responses to `llm_raw_response.txt`, which can be extremely useful for debugging:

```bash
# Check the file existence
ls -la llm_raw_response.txt

# View the raw response size
wc -l llm_raw_response.txt

# Find malformed JSON in the response
grep -n -A2 -B2 "}" llm_raw_response.txt | grep "{"
```

## Watching Log Files in Real-Time

To monitor logs as they happen:

```bash
# Start a Streamlit run with output to a log file
python -m streamlit run main.py 2>&1 | tee application.log

# Watch the log file in real-time with filtering
tail -f application.log | grep -i "error\|exception\|extracted"
```

## Common Issues and Indicators

| Issue | Log Indicator | Grep Command |
|-------|--------------|-------------|
| Missing Line Items | "Successfully extracted X line items" (where X is small) | `grep -i "extracted.*line items" log_output.txt` |
| JSON Parsing Failed | "Failed to parse JSON" | `grep -i "failed to parse" log_output.txt` |
| OCR Quality Issues | "Low confidence blocks" | `grep -i "low confidence" log_output.txt` |
| Credentials Missing | "No credentials" or "credentials not set" | `grep -i "no credential\|not set" log_output.txt` |
| Model Errors | "Error from OpenAI API" | `grep -i "error from.*api" log_output.txt` |

## Advanced Debugging Session Example

Here's a full debugging session for a common issue:

```bash
# 1. Capture log output
python -m streamlit run main.py > debug_log.txt 2>&1

# 2. Process the invoice in the UI

# 3. Check the processing flow
grep -i "\[.*_node\]" debug_log.txt | sort

# 4. Look for specific errors
grep -i "error\|exception" debug_log.txt

# 5. Check line item extraction 
grep -i "line items" debug_log.txt

# 6. Examine the raw LLM response
jq . llm_raw_response.txt
```

## Streamlit-Specific Debugging

For Streamlit UI issues:

```bash
# Run with verbose logging
streamlit run main.py --logger.level=debug

# Check for widget interaction errors
grep -i "widget\|callback" debug_log.txt

# Look for session state issues
grep -i "session_state" debug_log.txt
```

---

*These debugging commands and techniques are specifically tailored to the AlphaGrep Invoice Processor application and its components.*