import datetime
import requests
from urllib.parse import quote
import json
import pandas as pd
import boto3
from intuitlib.client import AuthClient
from intuitlib.utils import get_auth_header
import xml.etree.ElementTree as ET
import os
from abc import ABC, abstractmethod
from typing import Dict, Any
import io

# S3 bucket and key for credentials
S3_BUCKET = "pillar-credentials"
S3_KEY = "qb_creds.json"

# Local path for invoice data
INVOICE_LOCAL_PATH = "json_outputs/invoice_enriched.json"
INVOICE_LOCAL_PATH = r"C:\Users\<USER>\Documents\tmp\pikernel-AG\tmp_227ajti_enriched.json"

# Local path for temporary storage of credentials
TMP_CREDENTIALS_FILE = "/tmp/qb_creds.json"

def load_credentials_from_s3():
    """Download credentials from S3 and load them"""
    s3_client = boto3.client('s3')
    
    try:
        # Download the file from S3 to the Lambda /tmp directory
        s3_client.download_file(S3_BUCKET, S3_KEY, TMP_CREDENTIALS_FILE)
        
        # Load the credentials from the downloaded file
        with open(TMP_CREDENTIALS_FILE, "r") as file:
            return json.load(file)
    except Exception as e:
        print(f"Error loading credentials from S3: {e}")
        raise

def save_credentials(credentials):
    """Save credentials locally and upload to S3"""
    # Save locally first
    with open(TMP_CREDENTIALS_FILE, "w") as file:
        json.dump(credentials, file, indent=4)
    
    # Upload to S3
    s3_client = boto3.client('s3')
    try:
        s3_client.upload_file(TMP_CREDENTIALS_FILE, S3_BUCKET, S3_KEY)
    except Exception as e:
        print(f"Error uploading credentials to S3: {e}")
        raise

# Load credentials
def load_credentials():
    return load_credentials_from_s3()

# Initialize credentials
creds = load_credentials()
CLIENT_ID = creds["client_id"]
CLIENT_SECRET = creds["client_secret"]
ENV = creds["environment"]
COMPANY_ID = creds["company_id"]
REFRESH_TOKEN = creds["refresh_token"]
VER = '75'
BASE_URL = f"https://{ENV}-quickbooks.api.intuit.com/v3/company/{COMPANY_ID}"


# # Allow insecure transport for local testing (remove for production)
# os.environ["OAUTHLIB_INSECURE_TRANSPORT"] = "1"


# Authenticate and refresh the access token
def refresh_access_token():
    global creds, REFRESH_TOKEN
    
    auth_client = AuthClient(
        client_id=CLIENT_ID,
        client_secret=CLIENT_SECRET,
        environment=ENV,
        redirect_uri="https://localhost:8000/callback"
    )

    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": get_auth_header(CLIENT_ID, CLIENT_SECRET)
    }

    body = {
        "grant_type": "refresh_token",
        "refresh_token": REFRESH_TOKEN
    }

    response = requests.post(auth_client.token_endpoint, headers=headers, data=body)
    print("Auth Response Status:", response.status_code)
    print("Auth Response Text:", response.text)

    try:
        creds_response = response.json()
        if "access_token" not in creds_response:
            raise Exception("Failed to refresh access token")

        # Update credentials file with new refresh token
        creds["refresh_token"] = creds_response["refresh_token"]
        REFRESH_TOKEN = creds_response["refresh_token"]
        save_credentials(creds)

        return creds_response["access_token"], creds_response["refresh_token"]
    except requests.exceptions.JSONDecodeError:
        raise Exception("Failed to parse authentication response")

def authenticate():
    creds = load_credentials()
    auth_client = AuthClient(
        client_id=creds["client_id"],
        client_secret=creds["client_secret"],
        environment=creds["environment"],
        redirect_uri="https://localhost:8000/callback"
    )

    headers = {
        "Content-Type": "application/x-www-form-urlencoded",
        "Authorization": get_auth_header(creds["client_id"], creds["client_secret"])
    }

    body = {
        "grant_type": "refresh_token",
        "refresh_token": creds["refresh_token"]
    }

    response = requests.post(auth_client.token_endpoint, headers=headers, data=body)

    if response.status_code == 200:
        creds_response = response.json()
        creds["refresh_token"] = creds_response["refresh_token"]
        save_credentials(creds)
        print(creds_response['access_token'])
        return creds_response["access_token"]

    else:
        raise Exception(f"Authentication failed: {response.text}")

def get_customers():
    try:
        access_token = authenticate()

        url = f"{BASE_URL}/query?query=SELECT * FROM Customer where Active = true"
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Accept": "application/json"
        }

        response = requests.get(url, headers=headers)  # Use GET instead of POST

        print("Get Customer Response:", response.status_code, response.text)

        try:
            if response.status_code == 200:
                data = response.json()
                print("Customer Query Response JSON:", data)  # Debugging
                customers = data.get("QueryResponse", {}).get("Customer", [])
                df_customers = pd.json_normalize(customers)
                # writer = pd.ExcelWriter('coa.xlsx', engine='xlsxwriter')
                df_customers.to_excel('customers.xlsx', index=False)
                # df_coa = pd.DataFrame(gl_accounts)
                print(df_customers)
            else:
                raise Exception(f"Error finding accounts: {response.text}")

        except requests.exceptions.JSONDecodeError:
            raise Exception("Failed to parse account query response")
    except Exception as e:
        print(f"Error: {e}")
    return df_customers

########################## find project code######################
def get_projectcode(project_name):
    #call get_customers and filter for IsProject is true
    df_project = get_customers()
    #check if


#****************************** end of project code



# Check if vendor exists
def find_vendor(vendor_name, access_token):
    url = f"{BASE_URL}/query?query=SELECT * FROM Vendor WHERE DisplayName = '{quote(vendor_name)}'"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Accept": "application/json"
    }

    print(f"Find Vendor URL: {url}")
    print(f"Find Vendor Headers: {headers}")

    response = requests.get(url, headers=headers)  # Use GET instead of POST

    print("Find Vendor Response:", response.status_code, response.text)

    try:
        if response.status_code == 200:
            data = response.json()
            print("Vendor Query Response JSON:", data)  # Debugging
            vendors = data.get("QueryResponse", {}).get("Vendor", [])
            return vendors[0] if vendors else None
        else:
            raise Exception(f"Error finding vendor: {response.text}")
    except requests.exceptions.JSONDecodeError:
        raise Exception("Failed to parse vendor query response")


# Create vendor if it does not exist
def create_vendor(vendor_name, bill_data, access_token):
    url = f"{BASE_URL}/vendor"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }
    address_line1 = bill_data['invoice_header']['supplier']['address']['street']
    address_city = bill_data['invoice_header']['supplier']['address']['city']
    address_state = bill_data['invoice_header']['supplier']['address']['state']
    address_zip = bill_data['invoice_header']['supplier']['address']['zip_code']
    address_country = bill_data['invoice_header']['supplier']['address']['country']
    address_email = bill_data['invoice_header']['supplier']['contact']['email']
    address_phone = bill_data['invoice_header']['supplier']['contact']['phone']

    payload = {
        "CompanyName": vendor_name,
        "DisplayName": vendor_name,
        "CurrencyRef": {
           "value": "USD"
        },
        "PrimaryEmailAddr": {"Address": address_email},
        "BillAddr": {
            "Line1": address_line1,
            "City": address_city,
            "CountrySubDivisionCode": address_state,
            "PostalCode": address_zip,
            "Country": address_country,
        },
    }
    response = requests.post(url, headers=headers, json=payload)
    print("Create Vendor Response:", response.status_code, response.text)

    try:
        if response.status_code == 200:
            # Use the new XML parser instead of direct XML parsing
            vendor_data = handle_qb_response(response.text, entity_type="Vendor")
            
            # Extract vendor ID and creation time from the parsed data
            if "Vendors" in vendor_data and vendor_data["Vendors"]:
                vendor_info = vendor_data["Vendors"][0]
                vendor_id = vendor_info.get("Id")
                create_time = vendor_info.get("MetaData", {}).get("CreateTime")
                print(f"Vendor ID: {vendor_id}")
                print(f"Create Time: {create_time}")
                return vendor_id, create_time
            else:
                raise Exception("Failed to extract vendor information from response")
        else:
            error_info = handle_qb_response(response.text)
            raise Exception(f"Failed to create vendor: {error_info}")
    except Exception as e:
        raise Exception(f"Failed to process vendor creation: {str(e)}")



def post_invoice(vendor_id, bill_data, access_token, payables_account):
    url = f"{BASE_URL}/bill"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json",
    }

    # Extract from json the values required for the payload
    bill_no = bill_data['invoice_header']['invoice_number']
    bill_date = bill_data['invoice_header']['invoice_date']
    due_date = bill_data['invoice_header']['due_date']

    # Get valid account IDs from QuickBooks
    df_coa = get_coa(access_token)
    valid_account_ids = set(df_coa['Id'].astype(str).tolist())
    print(f"Valid account IDs: {valid_account_ids}")

    # Default expense account if we can't find a match
    default_expense_account = None
    for account_id in valid_account_ids:
        matching_accounts = df_coa[df_coa['Id'] == account_id]
        if not matching_accounts.empty:
            account_type = matching_accounts.iloc[0].get('AccountType')
            if account_type == 'Expense':
                default_expense_account = account_id
                print(f"Found default expense account: {default_expense_account}")
                break
    
    if not default_expense_account:
        # If no expense account found, use the first account as fallback
        default_expense_account = list(valid_account_ids)[0] if valid_account_ids else "1"
        print(f"Using fallback default account: {default_expense_account}")

    expense_lines = []
    for item in bill_data['line_items']:
        account_id = item.get('account_id', default_expense_account)
        
        # Validate account ID exists in QuickBooks
        if account_id not in valid_account_ids:
            print(f"Warning: Account ID {account_id} not found in QuickBooks. Using default expense account.")
            account_id = default_expense_account
        
        line = {
            "Amount": item['total_price'],
            "DetailType": "AccountBasedExpenseLineDetail",
            "Description": item['description'],
            "AccountBasedExpenseLineDetail": {
                "AccountRef": {"value": account_id}
            }
        }
        expense_lines.append(line)

    payload = {
        "VendorRef": {"value": vendor_id},
        "APAccountRef": {"value": payables_account},
        "CurrencyRef": {"value": "USD"},
        "DocNumber": bill_no,
        "TxnDate": bill_date,  # Add transaction date
        "Line": expense_lines,
        "DueDate": due_date,
    }

    print("Payload:", payload)
    response = requests.post(url, headers=headers, json=payload)

    # Log full details of the response
    print("Post Invoice Response:", response.status_code, response.headers)
    print("Response Body:", response.text)

    # Use the new XML parser for response handling
    if response.status_code == 200:
        qb_response = handle_qb_response(response.text, entity_type="Bill")
        return qb_response
    else:
        qb_response = handle_qb_response(response.text, entity_type="Bill")
        raise Exception(f"Failed to post invoice: {qb_response}")

#Get list of general ledger accounts
def get_coa(access_token):
    """
    Retrieve the Chart of Accounts from QuickBooks and return as a DataFrame.
    
    Args:
        access_token (str): The OAuth access token for QuickBooks API
        
    Returns:
        pd.DataFrame: DataFrame containing the Chart of Accounts
        
    Raises:
        Exception: If there's an error retrieving or parsing the accounts
    """
    url = f"{BASE_URL}/query?query=SELECT * FROM Account"
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Accept": "application/json"
    }

    response = requests.get(url, headers=headers)
    print("Get Chart of Accounts Response:", response.status_code)

    try:
        if response.status_code == 200:
            data = response.json()
            gl_accounts = data.get("QueryResponse", {}).get("Account", [])
            if not gl_accounts:
                raise Exception("No accounts found in response")
                
            # Convert to DataFrame for easier manipulation
            df_coa = pd.DataFrame(gl_accounts)
            print(f"Retrieved {len(df_coa)} accounts from QuickBooks")
            
            # Save to Excel for reference/debugging
            df_coa.to_excel('coa.xlsx', index=False)
            return df_coa
        else:
            raise Exception(f"Error retrieving accounts: {response.text}")
    except requests.exceptions.JSONDecodeError:
        raise Exception("Failed to parse account query response")

def read_invoice_json_from_local(file_path=INVOICE_LOCAL_PATH):
    """Read invoice JSON from local file system"""
    try:
        with open(file_path, 'r') as file:
            return json.loads(file.read())
    except Exception as e:
        raise Exception(f"Failed to read JSON file {file_path}: {str(e)}")

# Main function
def main(file_path=INVOICE_LOCAL_PATH):
    try:
        print(f"Reading invoice data from local file: {file_path}")
        bill_data = read_invoice_json_from_local(file_path)
        
        access_token, _ = refresh_access_token()
        
        # Get chart of accounts and find Accounts Payable account
        df_coa = get_coa(access_token)
        search_name = 'Accounts Payable'
        matching_records = df_coa[df_coa['Name'].str.contains(search_name, case=False, na=False)]
        payables_account = matching_records['Id'].values[0] if not matching_records.empty else "**********"
        
        vendor_name = bill_data['invoice_header']['supplier']['name']
        vendor = find_vendor(vendor_name, access_token)
        if vendor:
            print(f"Vendor '{vendor_name}' found: {vendor['Id']}")
            vendor_id = vendor["Id"]
        else:
            vendor_id, vendor_create_time = create_vendor(vendor_name, bill_data, access_token)
            print(f"Vendor ID'{vendor_id}' created: {vendor_create_time}")

        invoice = post_invoice(vendor_id, bill_data, access_token, payables_account)
        print(f"Invoice posted successfully: {invoice}")
        return invoice
    except Exception as e:
        print(f"Error: {e}")
        raise e

def post_json_string_invoice(json_string="{}"):
    try:
        bill_data = json.loads(json_string)
        access_token, _ = refresh_access_token()
        
        # Get chart of accounts and find Accounts Payable account
        df_coa = get_coa(access_token)
        
        # Find a valid Accounts Payable account
        search_name = 'Accounts Payable'
        matching_records = df_coa[df_coa['Name'].str.contains(search_name, case=False, na=False)]
        
        if not matching_records.empty:
            payables_account = matching_records['Id'].values[0]
            print(f"Found Accounts Payable account: {payables_account}")
        else:
            # If no Accounts Payable account found, look for a Liability account
            liability_accounts = df_coa[df_coa['AccountType'].str.contains('Liability', case=False, na=False)]
            if not liability_accounts.empty:
                payables_account = liability_accounts['Id'].values[0]
                print(f"Using Liability account as fallback: {payables_account}")
            else:
                # Last resort fallback
                payables_account = df_coa['Id'].values[0] if not df_coa.empty else "1"
                print(f"Using fallback account: {payables_account}")
        
        vendor_name = bill_data['invoice_header']['supplier']['name']
        vendor = find_vendor(vendor_name, access_token)
        if vendor:
            print(f"Vendor '{vendor_name}' found: {vendor['Id']}")
            vendor_id = vendor["Id"]
        else:
            vendor_id, vendor_create_time = create_vendor(vendor_name, bill_data, access_token)
            print(f"Vendor ID'{vendor_id}' created: {vendor_create_time}")

        invoice = post_invoice(vendor_id, bill_data, access_token, payables_account)
        print(f"Invoice posted successfully: {invoice}")
        return invoice
    except Exception as e:
        print(f"Error: {e}")
        raise e



# Replace Lambda handler with simple script execution
if __name__ == "__main__":
    # Check if the default file exists
    if not os.path.exists(INVOICE_LOCAL_PATH):
        print(f"Warning: Default invoice file not found at {INVOICE_LOCAL_PATH}")
        print("Looking for any *_enriched.json files in the json_outputs directory...")
        
        # Try to find any enriched JSON files in the directory
        json_dir = os.path.dirname(INVOICE_LOCAL_PATH)
        if os.path.exists(json_dir):
            enriched_files = [f for f in os.listdir(json_dir) if f.endswith('_enriched.json')]
            if enriched_files:
                alternative_path = os.path.join(json_dir, enriched_files[0])
                print(f"Found alternative file: {alternative_path}")
                main(alternative_path)
            else:
                print(f"No enriched JSON files found in {json_dir}")
        else:
            print(f"Directory {json_dir} does not exist")
    else:
        main()

# XML parser utility functions and classes
def xml_to_dict(element: ET.Element) -> Dict[str, Any]:
    """Convert an XML element to a dictionary recursively."""
    def strip_ns(tag: str) -> str:
        return tag.split('}')[-1] if '}' in tag else tag

    result = {}
    for child in element:
        tag = strip_ns(child.tag)
        if len(child):
            result[tag] = xml_to_dict(child)
        else:
            result[tag] = child.text
    return result

# Strategy pattern for QB XML parsing
class QBXMLParserStrategy(ABC):
    @abstractmethod
    def parse(self, xml_data: str) -> dict:
        pass

class QBErrorParserStrategy(QBXMLParserStrategy):
    def parse(self, xml_data: str) -> dict:
        result = {}
        try:
            ns_map = {prefix: uri for (prefix, uri) in ET.iterparse(
                io.StringIO(xml_data),
                events=['start-ns']
            )}
            root = ET.fromstring(xml_data)
            fault = root.find(".//{*}Fault")
            if fault is not None:
                result["FaultType"] = fault.attrib.get("type", "Unknown")
                error = fault.find("{*}Error")
                if error is not None:
                    result["ErrorCode"] = error.attrib.get("code", "Unknown")
                    result["ErrorElement"] = error.attrib.get("element", "Unknown")
                    message = error.find("{*}Message")
                    detail = error.find("{*}Detail")
                    result["ErrorMessage"] = message.text if message is not None else None
                    result["ErrorDetail"] = detail.text if detail is not None else None
        except ET.ParseError:
            result["ParseError"] = "Invalid XML"
        return result or {"Status": "No error found"}

class QBSuccessParserStrategy(QBXMLParserStrategy):
    def __init__(self, entity_tag: str):
        self.entity_tag = entity_tag

    def parse(self, xml_data: str) -> dict:
        result = {}
        try:
            ns_map = {prefix: uri for (prefix, uri) in ET.iterparse(
                io.StringIO(xml_data),
                events=['start-ns']
            )}
            root = ET.fromstring(xml_data)

            tag_variants = [f".//{{{uri}}}{self.entity_tag}" for uri in ns_map.values()] + [f".//{self.entity_tag}"]
            entities = []
            for tag in tag_variants:
                entities = root.findall(tag)
                if entities:
                    break

            items = [xml_to_dict(entity) for entity in entities]
            result[self.entity_tag + "s"] = items
        except ET.ParseError:
            result["ParseError"] = "Invalid XML"
        return result or {"Status": f"No {self.entity_tag} data found"}

class QBResponseParser:
    def __init__(self, strategy: QBXMLParserStrategy):
        self._strategy = strategy

    def set_strategy(self, strategy: QBXMLParserStrategy):
        self._strategy = strategy

    def parse(self, xml_data: str) -> dict:
        return self._strategy.parse(xml_data)

def handle_qb_response(xml_data: str, entity_type: str = None) -> dict:
    """
    Parse QuickBooks XML response into a structured dictionary.
    
    Args:
        xml_data (str): The XML response from QuickBooks API
        entity_type (str, optional): The type of entity to extract (e.g., "Bill", "Vendor")
                                    If None, will try to determine from context
    
    Returns:
        dict: A structured dictionary containing the parsed response
    """
    if "<Fault" in xml_data or "<Fault " in xml_data:
        parser = QBResponseParser(QBErrorParserStrategy())
    else:
        parser = QBResponseParser(QBSuccessParserStrategy(entity_type or "Bill"))
    return parser.parse(xml_data)
