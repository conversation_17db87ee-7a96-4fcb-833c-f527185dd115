# StateGraph Best Practices Guide

This guide provides practical examples and best practices for extending the StateGraph pipeline in the AlphaGrep Invoice Processor application.

## Table of Contents

1. [Understanding StateGraph](#understanding-stategraph)
2. [Creating New Nodes](#creating-new-nodes)
3. [Defining and Adding Edges](#defining-and-adding-edges)
4. [Conditional Routing](#conditional-routing)
5. [Managing State](#managing-state)
6. [Testing and Debugging](#testing-and-debugging)
7. [Performance Considerations](#performance-considerations)
8. [Complete Examples](#complete-examples)

## Understanding StateGraph

The PiKernelPiKernal Invoice Processor uses a graph-based workflow system to process invoices through a series of steps. Each step is represented by a **node** function that transforms the application state. These nodes are connected by **edges** that define the processing flow.

### Core Components

- **Nodes**: Functions that process the state and return a modified state
- **Edges**: Connections between nodes that define the flow
- **Conditional Edges**: Special edges that route based on state values
- **State**: A Pydantic model that contains all data being processed

### Basic Flow

```
START → copy_file → google_ocr → invoice_type → [conditional routing] → ...
```

## Creating New Nodes

Nodes are Python functions that take a state dictionary as input and return a modified state dictionary.

### Node Function Template

```python
def my_new_node(state: dict) -> dict:
    """
    Process the state and return a modified version.
    
    Args:
        state: The current processing state dictionary
        
    Returns:
        dict: Modified state dictionary
    """
    print(f"[my_new_node] Processing state with keys: {list(state.keys())}")
    
    # Access state values
    some_value = state.get("some_key", "default_value")
    
    # Do processing
    result = process_data(some_value)
    
    # Update state with new values
    state["new_key"] = result
    
    return state
```

### Example: Creating a Currency Conversion Node

Let's create a node that converts invoice amounts to a standard currency:

```python
def currency_conversion_node(state: dict) -> dict:
    """
    Converts invoice amounts to a standard currency (USD).
    
    Args:
        state: The current processing state
        
    Returns:
        dict: State with converted currency amounts
    """
    print("[currency_conversion_node] Starting currency conversion")
    
    parsed_data = state.get("parsed_data", {})
    if not parsed_data:
        print("[currency_conversion_node] No parsed data available")
        return state
    
    # Get the invoice currency
    invoice_header = parsed_data.get("invoice_header", {})
    source_currency = invoice_header.get("currency", "USD")
    
    # Skip if already in USD
    if source_currency == "USD":
        print("[currency_conversion_node] Already in USD, no conversion needed")
        return state
    
    # Get conversion rate (in a real app, you would use an API)
    conversion_rates = {
        "EUR": 1.08,
        "GBP": 1.27,
        "CAD": 0.74,
        "AUD": 0.66,
        "JPY": 0.0069
    }
    
    rate = conversion_rates.get(source_currency)
    if not rate:
        print(f"[currency_conversion_node] No conversion rate for {source_currency}, skipping")
        return state
    
    # Convert the total amount
    if "total_amount" in invoice_header:
        original_amount = invoice_header["total_amount"]
        converted_amount = original_amount * rate
        
        # Store both values
        invoice_header["original_currency"] = source_currency
        invoice_header["original_amount"] = original_amount
        invoice_header["currency"] = "USD"
        invoice_header["total_amount"] = converted_amount
        
        print(f"[currency_conversion_node] Converted {original_amount} {source_currency} to {converted_amount} USD")
    
    # Convert line item amounts
    line_items = parsed_data.get("line_items", [])
    for item in line_items:
        if "total_price" in item:
            item["original_price"] = item["total_price"]
            item["total_price"] = item["total_price"] * rate
        
        if "unit_price" in item:
            item["original_unit_price"] = item["unit_price"]
            item["unit_price"] = item["unit_price"] * rate
    
    # Update state
    state["parsed_data"] = parsed_data
    state["currency_converted"] = True
    
    return state
```

### Node Design Best Practices

1. **Clear Documentation**: Include docstrings explaining the node's purpose
2. **Descriptive Logging**: Print log messages with the node name as prefix
3. **Defensive Programming**: Check if required data exists before processing
4. **Immutability**: Only modify the state you need to change
5. **Default Values**: Use `get()` with defaults to handle missing data
6. **Error Handling**: Catch exceptions to prevent pipeline failures

## Defining and Adding Edges

Edges define the flow between nodes in the StateGraph. They are added to the graph during initialization.

### Basic Edge Definition

```python
graph_wrapper.add_edge(source_node, target_node)
```

### Example: Adding the Currency Conversion Node

Let's add our currency conversion node to the pipeline:

```python
# In the graph construction code (e.g., in src/controllers/invoice_controller.py)

# Add all nodes
graph_wrapper.add_node("copy", copy_file_node)
graph_wrapper.add_node("google_ocr", google_vision_ocr_node)
graph_wrapper.add_node("invoice_type", invoice_type_classifier_node)
graph_wrapper.add_node("azure_ocr", azure_ocr_node)
graph_wrapper.add_node("compare_ocr", compare_ocr_lengths_node)
graph_wrapper.add_node("write_text", write_text_file_node)
graph_wrapper.add_node("llm_parser", llm_parser_node)
graph_wrapper.add_node("currency_conversion", currency_conversion_node)  # Add our new node
graph_wrapper.add_node("intent", intent_node)
graph_wrapper.add_node("frequency", frequency_node)
graph_wrapper.add_node("write_json", write_json_file_node)

# Add all edges
graph_wrapper.add_edge(START, "copy")
graph_wrapper.add_edge("copy", "google_ocr")
graph_wrapper.add_edge("google_ocr", "invoice_type")

# Add conditional edge with router function
graph_wrapper.add_conditional_edge("invoice_type", route_by_invoice_type)

# Define remaining path for ALPHAGREP invoices
graph_wrapper.add_edge("azure_ocr", "compare_ocr")
graph_wrapper.add_edge("compare_ocr", "write_text")

# Common path through LLM parser and enrichment nodes
graph_wrapper.add_edge("write_text", "llm_parser")
graph_wrapper.add_edge("llm_parser", "currency_conversion")  # Insert our new node
graph_wrapper.add_edge("currency_conversion", "intent")
graph_wrapper.add_edge("intent", "frequency")
graph_wrapper.add_edge("frequency", "write_json")
graph_wrapper.add_edge("write_json", END)
```

### Edge Definition Best Practices

1. **Keep Consistent**: Maintain a consistent pattern for adding edges
2. **Use Named Constants**: Use `START` and `END` for clarity
3. **Group by Flow**: Keep related edges together in the code
4. **Comments by Section**: Add comments to identify different flow segments
5. **Visualize First**: Sketch the graph on paper before implementing

## Conditional Routing

Conditional routing allows the StateGraph to take different paths based on the state values. This is implemented using router functions that return the name of the next node.

### Router Function Template

```python
def route_by_condition(state: dict) -> str:
    """
    Determines the next node based on a condition.
    
    Args:
        state: The current processing state
        
    Returns:
        str: Name of the next node
    """
    condition_value = state.get("some_condition", False)
    
    if condition_value:
        print(f"[route_by_condition] Condition is True, routing to node_a")
        return "node_a"
    else:
        print(f"[route_by_condition] Condition is False, routing to node_b")
        return "node_b"
```

### Example: Currency Conversion Routing

Let's create a router that skips currency conversion for certain invoice types:

```python
def route_currency_conversion(state: dict) -> str:
    """
    Determines whether to perform currency conversion based on invoice type.
    
    Args:
        state: The current processing state
        
    Returns:
        str: Name of the next node
    """
    # Skip conversion for certain suppliers
    parsed_data = state.get("parsed_data", {})
    supplier_name = parsed_data.get("invoice_header", {}).get("supplier", {}).get("name", "")
    
    # Get the currency
    currency = parsed_data.get("invoice_header", {}).get("currency", "USD")
    
    # Skip conversion for domestic suppliers or USD invoices
    domestic_suppliers = ["US Tech Inc", "American Services LLC", "Local Provider"]
    
    if currency == "USD" or any(supplier in supplier_name for supplier in domestic_suppliers):
        print(f"[route_currency_conversion] Skipping conversion for {supplier_name} (currency: {currency})")
        return "intent"  # Skip currency conversion
    else:
        print(f"[route_currency_conversion] Performing conversion for {supplier_name} (currency: {currency})")
        return "currency_conversion"  # Perform conversion
```

Then update the graph:

```python
# Update the edges to use conditional routing
graph_wrapper.add_edge("llm_parser", "currency_router")  # Add a virtual node for routing
graph_wrapper.add_conditional_edge("currency_router", route_currency_conversion)

# Both paths converge at intent node
graph_wrapper.add_edge("currency_conversion", "intent")
```

### Conditional Routing Best Practices

1. **Clear Logging**: Log the routing decision and reason
2. **Limited Conditions**: Keep routing logic simple and maintainable
3. **Default Route**: Always provide a default route to prevent deadlocks
4. **Descriptive Names**: Name router functions to indicate their purpose
5. **Avoid Side Effects**: Router functions should only make decisions, not modify state

## Managing State

The ProcessState class manages all data during the invoice processing pipeline. Understanding how to correctly update and access state is crucial.

### Example: Adding a New Field to ProcessState

If your nodes need to store new types of data, update the ProcessState class:

```python
# In src/state/state.py
from pydantic import BaseModel, Field
from typing import Dict, List, Optional

class ProcessState(BaseModel):
    input_folder: str = ""
    output_folder: str = ""
    filename: str = ""
    invoices: Dict[str, str] = Field(default_factory=dict)
    ocr_results: Dict[str, str] = Field(default_factory=dict)
    ocr_confidences: Dict[str, Dict[str, float]] = Field(default_factory=dict)
    selected_ocr_provider: str = ""
    selected_ocr_text: str = ""
    parsed_jsons: Dict[str, str] = Field(default_factory=dict)
    parsed_data: dict = Field(default_factory=dict)
    llm_provider: str = "openai"
    llm_model: str = "gpt-4o"
    invoice_type: str = "UNKNOWN"
    
    # Add new fields for currency conversion
    currency_converted: bool = False
    conversion_rates: Dict[str, float] = Field(default_factory=dict)
    original_currency: Optional[str] = None
    
    def update_invoice(self, filename: str, status: str):
        self.invoices[filename] = status

    def __str__(self):
        return self.model_dump_json(indent=2)

    class Config:
        # Allow extra keys to be included if needed.
        extra = "allow"
```

### State Management Best Practices

1. **Clear Structure**: Define fields with types in the ProcessState class
2. **Helper Methods**: Add methods for common state operations
3. **Immutable Updates**: Create new objects rather than modifying nested ones
4. **Consistent Access**: Use consistent patterns to access state values
5. **Default Values**: Always provide sensible defaults for new fields
6. **Type Annotations**: Use type hints to document state structure

## Testing and Debugging

Testing nodes and graphs is essential for maintaining a reliable pipeline.

### Example: Testing a Node Function

```python
def test_currency_conversion_node():
    """Test the currency conversion node with a sample state."""
    # Create a test state
    test_state = {
        "parsed_data": {
            "invoice_header": {
                "currency": "EUR",
                "total_amount": 100.0
            },
            "line_items": [
                {"total_price": 50.0, "unit_price": 25.0},
                {"total_price": 50.0, "unit_price": 25.0}
            ]
        }
    }
    
    # Run the node
    result_state = currency_conversion_node(test_state)
    
    # Verify the results
    assert result_state["currency_converted"] == True
    assert result_state["parsed_data"]["invoice_header"]["currency"] == "USD"
    assert result_state["parsed_data"]["invoice_header"]["original_currency"] == "EUR"
    assert result_state["parsed_data"]["invoice_header"]["original_amount"] == 100.0
    assert abs(result_state["parsed_data"]["invoice_header"]["total_amount"] - 108.0) < 0.01
    
    # Check line items
    for item in result_state["parsed_data"]["line_items"]:
        assert "original_price" in item
        assert abs(item["total_price"] - 54.0) < 0.01
    
    print("Currency conversion node test passed!")
```

### Example: Debugging the StateGraph

Add a debug node that outputs the current state at any point in the pipeline:

```python
def debug_state_node(name: str):
    """
    Create a debug node that prints the current state.
    
    Args:
        name: Identifier for this debug node
        
    Returns:
        function: A node function that logs state information
    """
    def _debug_node(state: dict) -> dict:
        print(f"\n[DEBUG {name}] =====================")
        print(f"[DEBUG {name}] State keys: {list(state.keys())}")
        
        if "parsed_data" in state:
            parsed_data = state["parsed_data"]
            print(f"[DEBUG {name}] parsed_data keys: {list(parsed_data.keys())}")
            
            if "invoice_header" in parsed_data:
                print(f"[DEBUG {name}] invoice_header: {parsed_data['invoice_header']}")
            
            if "line_items" in parsed_data:
                print(f"[DEBUG {name}] line_items count: {len(parsed_data['line_items'])}")
                if parsed_data["line_items"]:
                    print(f"[DEBUG {name}] First line item: {parsed_data['line_items'][0]}")
        
        print(f"[DEBUG {name}] =====================\n")
        return state
    
    return _debug_node
```

Add the debug node to your graph:

```python
# Add debug nodes at critical points
graph_wrapper.add_node("debug_before_currency", debug_state_node("before_currency"))
graph_wrapper.add_node("debug_after_currency", debug_state_node("after_currency"))

# Update edges to include debug nodes
graph_wrapper.add_edge("llm_parser", "debug_before_currency")
graph_wrapper.add_edge("debug_before_currency", "currency_conversion")
graph_wrapper.add_edge("currency_conversion", "debug_after_currency")
graph_wrapper.add_edge("debug_after_currency", "intent")
```

### Testing and Debugging Best Practices

1. **Isolated Tests**: Test each node in isolation
2. **Synthetic States**: Create test states with known values
3. **Assert Expectations**: Verify specific outcomes, not just execution
4. **Debug Nodes**: Insert debug nodes to inspect state at critical points
5. **Log State Changes**: Log before and after states for comparison
6. **Test Edge Cases**: Test with empty data, missing fields, etc.

## Performance Considerations

### Node Performance Best Practices

1. **Minimize Memory Usage**: Don't duplicate large data unnecessarily
2. **Selective Updates**: Only modify what's needed in the state
3. **Lazy Loading**: Load large resources only when needed
4. **Clean Up Temporary Data**: Remove temporary data from state when done
5. **Profile Critical Nodes**: Measure performance of computationally intensive nodes

### Example: Optimizing a Node

```python
def optimized_currency_conversion_node(state: dict) -> dict:
    """Optimized version of the currency conversion node."""
    parsed_data = state.get("parsed_data")
    if not parsed_data:
        return state
    
    # Early return if already converted
    if state.get("currency_converted", False):
        return state
    
    invoice_header = parsed_data.get("invoice_header", {})
    source_currency = invoice_header.get("currency")
    
    # Skip if no currency or already USD
    if not source_currency or source_currency == "USD":
        return state
    
    # Rest of the conversion logic...
```

## Complete Examples

### Example 1: Adding PDF Signature Verification

Let's create a complete example of adding a node to verify PDF signatures:

```python
# In src/models/pdf_verifier.py
import PyPDF2

class PDFSignatureVerifier:
    """Verifies the digital signatures in PDF documents."""
    
    def verify_signature(self, pdf_path: str) -> dict:
        """
        Verify the signature in a PDF document.
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            dict: Verification result with status and details
        """
        try:
            with open(pdf_path, 'rb') as file:
                pdf = PyPDF2.PdfReader(file)
                
                # Check if the PDF has a signature
                if "/Sig" in pdf.trailer["/Root"]["/AcroForm"].get("/Fields", []):
                    # In a real app, you would validate the signature cryptographically
                    # This is a simplified example
                    return {
                        "has_signature": True,
                        "verified": True,
                        "signer": "Example Signer",
                        "signature_date": "2025-03-01",
                        "is_valid": True
                    }
                else:
                    return {
                        "has_signature": False,
                        "verified": False,
                        "is_valid": False
                    }
        except Exception as e:
            return {
                "has_signature": False,
                "verified": False,
                "is_valid": False,
                "error": str(e)
            }
```

```python
# In src/state/nodes.py
from src.models.pdf_verifier import PDFSignatureVerifier

def signature_verification_node(state: dict) -> dict:
    """
    Verifies digital signatures in the invoice PDF.
    
    Args:
        state: Processing state
        
    Returns:
        dict: Updated state with signature verification results
    """
    print("[signature_verification_node] Verifying PDF signature")
    
    output_folder = state["output_folder"]
    filename = state["filename"]
    file_path = os.path.join(output_folder, filename)
    
    # Verify the signature
    verifier = PDFSignatureVerifier()
    result = verifier.verify_signature(file_path)
    
    # Add to state
    state["signature_verification"] = result
    
    print(f"[signature_verification_node] Signature verification result: {result['is_valid']}")
    
    return state

def route_by_signature_status(state: dict) -> str:
    """Route based on signature verification status."""
    verification = state.get("signature_verification", {})
    is_valid = verification.get("is_valid", False)
    
    if is_valid:
        print("[route_by_signature_status] Valid signature detected, proceeding with normal processing")
        return "copy"  # Continue with normal processing
    else:
        print("[route_by_signature_status] Invalid or missing signature, flagging for review")
        return "flag_for_review"  # Route to special handling
```

```python
# In src/controllers/invoice_controller.py
# Add the new nodes
graph_wrapper.add_node("signature_verification", signature_verification_node)
graph_wrapper.add_node("flag_for_review", lambda state: {**state, "needs_review": True})

# Add a conditional edge at the start of the pipeline
graph_wrapper.add_edge(START, "signature_verification")
graph_wrapper.add_conditional_edge("signature_verification", route_by_signature_status)

# Connect the flag_for_review node
graph_wrapper.add_edge("flag_for_review", "copy")  # Continue processing but with flag set
```

### Example 2: Adding a Node Chain for PO Matching

Let's implement a feature to match invoices with purchase orders:

```python
# In src/models/po_matcher.py
import fuzzywuzzy.fuzz as fuzz

class POMatchingService:
    """Service for matching invoices with purchase orders."""
    
    def __init__(self, po_database_path: str = None):
        """Initialize with path to PO database (simplified example)."""
        # In a real app, this would connect to a database
        # For this example, we'll use a hard-coded dict
        self.po_database = {
            "PO-2025-001": {
                "supplier": "Guava Tech, Inc.",
                "total_amount": 5000.00,
                "status": "approved",
                "line_items": [
                    {"description": "Cloud Hosting", "amount": 3000.00},
                    {"description": "Technical Support", "amount": 2000.00}
                ]
            },
            "PO-2025-002": {
                "supplier": "Acme Corp",
                "total_amount": 10000.00,
                "status": "pending",
                "line_items": [
                    {"description": "Software License", "amount": 8000.00},
                    {"description": "Implementation", "amount": 2000.00}
                ]
            }
        }
    
    def find_matching_po(self, invoice_data: dict) -> dict:
        """
        Find a matching purchase order for the invoice.
        
        Args:
            invoice_data: Parsed invoice data
            
        Returns:
            dict: Matching result with PO info if found
        """
        invoice_header = invoice_data.get("invoice_header", {})
        supplier_name = invoice_header.get("supplier", {}).get("name", "")
        total_amount = invoice_header.get("total_amount", 0)
        
        best_match = None
        best_score = 0
        
        for po_number, po_data in self.po_database.items():
            # Check supplier name similarity
            supplier_similarity = fuzz.ratio(supplier_name.lower(), po_data["supplier"].lower())
            
            # Check amount similarity (within 10%)
            amount_diff_percent = abs(total_amount - po_data["total_amount"]) / po_data["total_amount"] * 100
            amount_match = amount_diff_percent <= 10
            
            # Calculate overall match score
            score = supplier_similarity * 0.8 + (100 if amount_match else 0) * 0.2
            
            if score > best_score and score > 70:  # Threshold for a match
                best_score = score
                best_match = {
                    "po_number": po_number,
                    "po_data": po_data,
                    "match_score": score
                }
        
        if best_match:
            return {
                "match_found": True,
                "po_number": best_match["po_number"],
                "supplier": best_match["po_data"]["supplier"],
                "po_amount": best_match["po_data"]["po_amount"],
                "match_score": best_match["match_score"],
                "status": best_match["po_data"]["status"]
            }
        else:
            return {
                "match_found": False
            }
```

```python
# In src/state/nodes.py
from src.models.po_matcher import POMatchingService

def po_matching_node(state: dict) -> dict:
    """
    Matches invoices with purchase orders.
    
    Args:
        state: Processing state
        
    Returns:
        dict: Updated state with PO matching results
    """
    print("[po_matching_node] Looking for matching purchase orders")
    
    parsed_data = state.get("parsed_data", {})
    if not parsed_data:
        print("[po_matching_node] No parsed data available")
        return state
    
    # Create PO matching service
    po_service = POMatchingService()
    
    # Find matching PO
    match_result = po_service.find_matching_po(parsed_data)
    
    # Add to state
    state["po_matching"] = match_result
    
    if match_result["match_found"]:
        print(f"[po_matching_node] Matched invoice to PO: {match_result['po_number']} (score: {match_result['match_score']:.2f})")
    else:
        print("[po_matching_node] No matching PO found")
    
    return state

def route_by_po_status(state: dict) -> str:
    """Route based on PO matching and approval status."""
    po_matching = state.get("po_matching", {})
    
    if not po_matching.get("match_found", False):
        print("[route_by_po_status] No matching PO, requires approval")
        return "flag_for_approval"
    
    po_status = po_matching.get("status", "unknown")
    
    if po_status == "approved":
        print("[route_by_po_status] Matched to approved PO, proceeding to payment")
        return "prepare_payment"
    else:
        print(f"[route_by_po_status] Matched to PO with status '{po_status}', needs review")
        return "flag_for_review"
```

```python
# In graph construction
# Add the new nodes
graph_wrapper.add_node("po_matching", po_matching_node)
graph_wrapper.add_node("flag_for_approval", lambda state: {**state, "needs_approval": True})
graph_wrapper.add_node("prepare_payment", payment_preparation_node)  # Assume this exists
graph_wrapper.add_node("flag_for_review", lambda state: {**state, "needs_review": True})

# Add the PO matching after parsing but before enrichment
graph_wrapper.add_edge("llm_parser", "po_matching")
graph_wrapper.add_conditional_edge("po_matching", route_by_po_status)

# Connect the new paths
graph_wrapper.add_edge("flag_for_approval", "intent")  # Continue with regular flow
graph_wrapper.add_edge("flag_for_review", "intent")    # Continue with regular flow
graph_wrapper.add_edge("prepare_payment", "intent")    # Continue with regular flow
```

These examples demonstrate how to extend the StateGraph with complex features while maintaining the pipeline's structure and following best practices.