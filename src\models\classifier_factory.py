# src/models/classifier_factory.py
from src.models.classifiers import InvoiceClassifier, WordCountClassifier


class ClassifierFactory:
    """Factory for creating invoice classifiers"""

    @staticmethod
    def create_classifier(classifier_type: str = "word_count", **kwargs) -> InvoiceClassifier:
        """Create a classifier based on type"""
        if classifier_type == "word_count":
            threshold = kwargs.get("threshold", 12000)
            return WordCountClassifier(threshold=threshold)
        else:
            raise ValueError(f"Unsupported classifier type: {classifier_type}")