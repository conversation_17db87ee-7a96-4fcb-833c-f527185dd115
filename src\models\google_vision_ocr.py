import os
import io
from pathlib import Path
from google.cloud import vision
from pdf2image import convert_from_path
from json.decoder import JSONDecodeError
from json import dumps, loads

from src.models.ocr_result import OCRResult, TextBlock

class GoogleVisionOCR:
    def __init__(self):
        # First try to get credentials from environment variable
        cred_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")
        # print(f"[DEBUG] GOOGLE_APPLICATION_CREDENTIALS: {cred_path}")

        # If not set, try to find the credentials file in the project root
        if not cred_path:
            json_content = os.getenv("GOOGLE_CLOUD_CREDENTIALS_JSON")
            #print("[DEBUG] GOOGLE_CLOUD_CREDENTIALS_JSON: ")
            #print(r"{}".format(json_content))
            json_content = loads(json_content) if json_content else None

            if not json_content:
                # Look for credential file in project root
                project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../..'))
                # print(f"[DEBUG] Project root directory: {project_root}")

                potential_cred_files = [
                    f for f in os.listdir(project_root)
                    if f.endswith('.json') and 'lucidate' in f
                ]
                # print(f"[DEBUG] Potential credential files in project root: {potential_cred_files}")

                if potential_cred_files:
                    cred_path = os.path.join(project_root, potential_cred_files[0])
                    # print(f"[DEBUG] Selected credential file: {cred_path}")
                    # Set the environment variable for the duration of this process
                    os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = cred_path
                    print(f"[GoogleVisionOCR] Found credentials file: {cred_path}")
            else:
                # Write the JSON content to a temporary file
                cred_path = os.path.join("temp_creds.json")
                print(f"[DEBUG] Writing JSON content to temporary file: {cred_path}")
                with open(cred_path, "w") as f:
                    f.write(dumps(json_content))
                # Set the environment variable for the duration of this process
                os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = cred_path
                print(f"[GoogleVisionOCR] Using credentials from environment variable")
                print(f"[GoogleVisionOCR] Wrote JSON content to temporary file: {cred_path}")

        if not cred_path:
            # Still no credential path found
            self.client = None
            print("[GoogleVisionOCR] GOOGLE_APPLICATION_CREDENTIALS not set - OCR functionality will be limited")
        else:
            print("[GoogleVisionOCR] GOOGLE_APPLICATION_CREDENTIALS =", cred_path)
            try: 
                self.client = vision.ImageAnnotatorClient()
            except JSONDecodeError as e:
                print(f"[GoogleVisionOCR] Error initializing Google Cloud Vision client: {e}")
                # print text near the error position
                pos = e.pos
                with open(cred_path, "r") as f:
                    content = f.read()
                    print(f"[GoogleVisionOCR] Error position: {pos}")
                    print(f"[GoogleVisionOCR] Near error: {content[pos-100:pos+100]}")
                raise(e)
            print("[GoogleVisionOCR] Initialized Google Cloud Vision client.")

    def extract_text(self, file_path: str) -> OCRResult:
        path = Path(file_path)
        if not path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        if path.suffix.lower() == ".pdf":
            print(f"[GoogleVisionOCR] Processing PDF file {file_path} by converting pages to images.")
            try:
                images = convert_from_path(str(path))
                print(f"[GoogleVisionOCR] Converted PDF to {len(images)} images.")
            except Exception as e:
                raise Exception(f"Error converting PDF to images: {e}")

            all_text_blocks = []  # You can build a list of text blocks if needed
            full_text_accum = []
            for idx, image in enumerate(images):
                print(f"[GoogleVisionOCR] Processing page {idx + 1}/{len(images)}")
                img_byte_arr = io.BytesIO()
                image.save(img_byte_arr, format="JPEG")
                content = img_byte_arr.getvalue()

                image_obj = vision.Image(content=content)
                if self.client:
                    response = self.client.text_detection(image=image_obj)
                    if response.error.message:
                        raise Exception(f"Google Vision error on page {idx+1}: {response.error.message}")
                    texts = response.text_annotations
                    if texts:
                        page_text = texts[0].description
                        full_text_accum.append(page_text)
                    else:
                        print(f"[GoogleVisionOCR] No text detected on page {idx+1}.")
            final_text = "\n".join(full_text_accum)
            return OCRResult(final_text.strip(), all_text_blocks, provider="Google Vision")
        else:
            print(f"[GoogleVisionOCR] Processing image file {file_path} directly.")
            with open(file_path, "rb") as image_file:
                content = image_file.read()
            # Updated to always return an OCRResult object
            text_blocks, text_str = self._detect_text_blocks(content, page_number=1)
            return OCRResult(text_str, text_blocks, provider="Google Vision")

    def _detect_text_blocks(self, content: bytes, page_number: int):
        image = vision.Image(content=content)
        response = self.client.document_text_detection(image=image)
        if response.error.message:
            raise Exception(f"[GoogleVisionOCR] {response.error.message}")

        full_text = response.full_text_annotation.text or ""
        text_blocks = []

        for page_idx, page in enumerate(response.full_text_annotation.pages):
            actual_page_number = page_number
            for block in page.blocks:
                block_text = ""
                for paragraph in block.paragraphs:
                    paragraph_text = ""
                    for word in paragraph.words:
                        paragraph_text += "".join([symbol.text for symbol in word.symbols])
                        paragraph_text += " "
                    block_text += paragraph_text.strip() + "\n"

                block_conf = block.confidence  # Expected to be a float between 0 and 1
                bb = block.bounding_box
                if bb and len(bb.vertices) == 4:
                    min_x = min(v.x for v in bb.vertices)
                    max_x = max(v.x for v in bb.vertices)
                    min_y = min(v.y for v in bb.vertices)
                    max_y = max(v.y for v in bb.vertices)
                    position = {
                        "left": float(min_x),
                        "top": float(min_y),
                        "width": float(max_x - min_x),
                        "height": float(max_y - min_y)
                    }
                else:
                    position = {"left": 0, "top": 0, "width": 0, "height": 0}

                text_block = TextBlock(
                    text=block_text.strip(),
                    confidence=block_conf,
                    block_type="LINE",
                    page=actual_page_number,
                    position=position,
                    provider_details={}
                )
                text_block.provider_details["gvision_block_id"] = f"page{actual_page_number}_block{block.block_type}"
                text_blocks.append(text_block)

        return text_blocks, full_text
