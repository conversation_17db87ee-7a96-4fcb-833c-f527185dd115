import os
import tempfile
import shutil
from typing import List, Any
from langgraph.graph import START, END
from src.state.state import ProcessState
from src.state.graph import StateGraphWrapper
from src.state.nodes import (
    copy_file_node,
    google_vision_ocr_node,
    azure_ocr_node,
    write_text_file_node,
    llm_parser_node,
    invoice_type_classifier_node,
    compare_ocr_lengths_node,
    route_by_invoice_type,
    intent_node,
    project_codes_node,
    frequency_node,
    write_json_file_node,
    amazon_textract_node,
    check_lineitems,
    post_bill_node,
    detect_if_invoice_node
)
from src.utils.file_utils import list_files
from src.models.chart_of_accounts import ChartOfAccounts
from src.models.ledger import LedgerRepository
# these will be used to log the output of the graph in S3 bucket:
from io import StringIO
from pathlib import Path
from src.models.s3bucket import S3BucketRepository, FileLike
from src.models.queue_poller_factory import QueuePollerFactory
from urllib.parse import unquote_plus
from contextlib import redirect_stderr, redirect_stdout
from dotenv import load_dotenv

# Load environment variables if not already loaded
load_dotenv()

def route_by_invoice_detection(state: dict) -> str:
    """
    Route based on invoice detection result.
    Routes to END if document is not an invoice, otherwise continues to llm_parser.
    """
    is_invoice = state.get("is_invoice", False)

    if not is_invoice:
        print(f"[route_by_invoice_detection] Document is not an invoice, routing to END")
        return END
    else:
        print(f"[route_by_invoice_detection] Document is an invoice, continuing to llm_parser")
        return "llm_parser"

class InvoiceController:
    def __init__(self, input_folder: str, output_folder: str):
        self.input_folder = input_folder
        self.output_folder = output_folder
        # Initialize Chart of Accounts
        try:
            self.chart_of_accounts = ChartOfAccounts.create("Excel")
            self.sub_account_colors = self.chart_of_accounts.generate_colors()
            print(f"[InvoiceController] Initialized Chart of Accounts with {len(self.sub_account_colors)} sub-accounts")
        except Exception as e:
            print(f"[InvoiceController] Error initializing Chart of Accounts: {e}")
            self.chart_of_accounts = None
            self.sub_account_colors = {}

        # Store the workflow graph image
        self.workflow = None
        
        # Get supported file types from environment or use defaults
        supported_types_str = os.getenv("SUPPORTED_FILE_TYPES", "pdf,png,jpg,jpeg,tiff,tif")
        self.supported_extensions = [f".{ext.strip()}" for ext in supported_types_str.lower().split(",")]

    def process_invoices(self, files: List[Any], provider: str, model_name: str, ledger: LedgerRepository, intent_memos: List[Any] = None, project_code_memos: List[Any] = None):
        """
        Process uploaded invoice files using the specified LLM provider and model.

        Args:
            files: List of Streamlit UploadedFile objects
            provider: LLM provider name (e.g., 'OpenAI', 'Anthropic')
            model_name: Name of the specific model to use

        Returns:
            dict: Processing results
        """
        results = {}

        for uploaded_file in files:
            # Validate file extension
            file_extension = Path(uploaded_file.name).suffix.lower()
            
            if file_extension not in self.supported_extensions:
                print(f"[InvoiceController] Unsupported file type: {file_extension}")
                results[uploaded_file.name] = {"error": f"Unsupported file type: {file_extension}"}
                continue

            # Create a temporary file from the uploaded content
            with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
                tmp_file.write(uploaded_file.read())
                tmp_file_path = tmp_file.name
                tmp_file_stem = Path(tmp_file_path).stem

            with redirect_stdout(StringIO()) as f_stdout, redirect_stderr(StringIO()) as f_stderr:
                try:
                    # Process the invoice using the existing flow
                    filename = uploaded_file.name

                    # Create output directory if it doesn't exist
                    os.makedirs(self.output_folder, exist_ok=True)

                    # Update state with file information
                    state = ProcessState(
                        input_folder=os.path.dirname(tmp_file_path),
                        output_folder=self.output_folder,
                        filename=os.path.basename(tmp_file_path),
                        llm_provider=provider.lower(),
                        llm_model=model_name,
                        chart_of_accounts=self.chart_of_accounts,
                        intent_memos=intent_memos,
                        project_code_memos=project_code_memos
                    )

                    # Create graph
                    graph_wrapper = StateGraphWrapper()

                    # Add nodes
                    graph_wrapper.add_node("copy", copy_file_node)
                    graph_wrapper.add_node("google_ocr", google_vision_ocr_node)
                    graph_wrapper.add_node("invoice_type", invoice_type_classifier_node)
                    graph_wrapper.add_node("azure_ocr", azure_ocr_node)
                    graph_wrapper.add_node("compare_ocr", compare_ocr_lengths_node)
                    graph_wrapper.add_node("write_text", write_text_file_node)
                    graph_wrapper.add_node("detect_if_invoice", detect_if_invoice_node)
                    graph_wrapper.add_node("llm_parser", llm_parser_node)
                    graph_wrapper.add_node("amazon_textract", amazon_textract_node)
                    graph_wrapper.add_node("intent", intent_node)
                    graph_wrapper.add_node("project_codes", project_codes_node)
                    graph_wrapper.add_node("frequency", frequency_node)
                    graph_wrapper.add_node("post_bill", post_bill_node)
                    graph_wrapper.add_node("write_json", write_json_file_node)

                    # Add fixed edges
                    graph_wrapper.add_edge(START, "copy")
                    graph_wrapper.add_edge("copy", "google_ocr")
                    graph_wrapper.add_edge("google_ocr", "invoice_type")

                    # Add conditional edge with router function
                    graph_wrapper.add_conditional_edge("invoice_type", route_by_invoice_type)

                    # Define remaining path for ALPHAGREP invoices
                    graph_wrapper.add_edge("azure_ocr", "compare_ocr")
                    graph_wrapper.add_edge("compare_ocr", "write_text")

                    # Common path through LLM parser and enrichment nodes
                    graph_wrapper.add_edge("write_text", "detect_if_invoice")
                    graph_wrapper.add_conditional_edge("detect_if_invoice", route_by_invoice_detection)
                    graph_wrapper.add_conditional_edge(
                        "llm_parser", 
                        check_lineitems,
                        {
                            True: "amazon_textract",
                            False: "intent"
                        }
                    )
                    graph_wrapper.add_edge("amazon_textract", "write_text")
                    graph_wrapper.add_edge("intent", "project_codes")
                    graph_wrapper.add_edge("project_codes", "frequency")
                    graph_wrapper.add_edge("frequency", "post_bill")
                    graph_wrapper.add_edge("post_bill", "write_json")
                    graph_wrapper.add_edge("write_json", END)

                    # Get workflow graph for visualization
                    graph_wrapper.compile()
                    # Get workflow graph for visualization
                    try:
                        # Attempt to generate a workflow graph visualization.
                        # NOTE: The previous call to get_graph(xray=True) is removed since it is no longer available.
                        self.workflow = graph_wrapper.app.get_graph(xray=True).draw_mermaid_png()
                    except Exception as graph_error:
                        print(f"[InvoiceController] Error generating workflow graph: {graph_error}")
                        self.workflow = None

                    # Run the graph
                    final_state = graph_wrapper.run(state)

                    # Store results with original filename as key
                    results[filename] = final_state

                    # Write to ledger
                    ledger.create(final_state.parsed_data)

                    # Clean up temp file
                    os.unlink(tmp_file_path)

                except Exception as e:
                    print(f"[InvoiceController] Error processing {uploaded_file.name}: {str(e)}")
                    import traceback
                    traceback.print_exc()
                    results[uploaded_file.name] = {"error": str(e)}

                # upload log files to S3 bucket
                stdout_log = FileLike(f"{tmp_file_stem}_stdout.log", f_stdout.getvalue().encode("utf-8"))
                stderr_log = FileLike(f"{tmp_file_stem}_stderr.log", f_stderr.getvalue().encode("utf-8"))
                s3_bucket = S3BucketRepository()
                s3_bucket.create("pikernel-debug-results", f"{tmp_file_stem}_stdout.log", stdout_log)
                s3_bucket.create("pikernel-debug-results", f"{tmp_file_stem}_stderr.log", stderr_log)
            
            # print stdout and stderr
            print("stdout:")
            print(f_stdout.getvalue())
            print("stderr:")
            print(f_stderr.getvalue())

        return results

    def process_invoices_from_folder(self, llm_provider: str, llm_model: str):
        """
        Process all invoice files in the input folder.

        Args:
            llm_provider: LLM provider name (e.g., 'openai', 'anthropic')
            llm_model: Name of the specific model to use
        """
        files = list_files(self.input_folder)
        if not files:
            print("[InvoiceController] No invoice files found in the input folder.")
            return

        results = {}
        for filename in files:
            state = ProcessState(
                input_folder=self.input_folder,
                output_folder=self.output_folder,
                filename=filename,
                llm_provider=llm_provider,
                llm_model=llm_model,
                chart_of_accounts=self.chart_of_accounts
            )

            # Build graph with conditional routing
            graph_wrapper = StateGraphWrapper()

            # Add nodes
            graph_wrapper.add_node("copy", copy_file_node)
            graph_wrapper.add_node("google_ocr", google_vision_ocr_node)
            graph_wrapper.add_node("invoice_type", invoice_type_classifier_node)
            graph_wrapper.add_node("azure_ocr", azure_ocr_node)
            graph_wrapper.add_node("compare_ocr", compare_ocr_lengths_node)
            graph_wrapper.add_node("write_text", write_text_file_node)
            graph_wrapper.add_node("detect_if_invoice", detect_if_invoice_node)
            graph_wrapper.add_node("llm_parser", llm_parser_node)
            graph_wrapper.add_node("amazon_textract", amazon_textract_node)
            graph_wrapper.add_node("intent", intent_node)
            graph_wrapper.add_node("frequency", frequency_node)
            graph_wrapper.add_node("post_bill", post_bill_node)
            graph_wrapper.add_node("write_json", write_json_file_node)

            # Add fixed edges
            graph_wrapper.add_edge(START, "copy")
            graph_wrapper.add_edge("copy", "google_ocr")
            graph_wrapper.add_edge("google_ocr", "invoice_type")

            # Add conditional edge with router function
            graph_wrapper.add_conditional_edge("invoice_type", route_by_invoice_type)

            # Define remaining path for ALPHAGREP invoices
            graph_wrapper.add_edge("azure_ocr", "compare_ocr")
            graph_wrapper.add_edge("compare_ocr", "write_text")

            # Common path through LLM parser and enrichment nodes
            graph_wrapper.add_edge("write_text", "detect_if_invoice")
            graph_wrapper.add_conditional_edge("detect_if_invoice", route_by_invoice_detection)
            graph_wrapper.add_conditional_edge(
                "llm_parser", 
                check_lineitems,
                {
                    True: "amazon_textract",
                    False: "intent"
                }
            )
            graph_wrapper.add_edge("amazon_textract", "write_text")
            graph_wrapper.add_edge("intent", "frequency")
            graph_wrapper.add_edge("frequency", "post_bill")
            graph_wrapper.add_edge("post_bill", "write_json")
            graph_wrapper.add_edge("write_json", END)

            # Store workflow graph for visualization
            # Get workflow graph for visualization
            try:
                # Attempt to generate a workflow graph visualization.
                self.workflow = graph_wrapper.app.get_graph(xray=True).draw_mermaid_png()
            except Exception as graph_error:
                print(f"[InvoiceController] Error generating workflow graph: {graph_error}")
                self.workflow = None

            final_state = graph_wrapper.run(state)
            final_state_dict = final_state.model_dump()

            results[filename] = final_state_dict
            print(f"[InvoiceController] Processed {filename}:\n{final_state}")

            # Log the path to the enriched JSON file
            if "enriched_json_path" in final_state:
                print(f"[InvoiceController] Enriched JSON saved to: {final_state['enriched_json_path']}")

        return results
    
    def process_invoices_in_s3_bucket(
            self, 
            llm_provider: str, 
            llm_model: str, 
            poll_timeout: int = 15*60 # wait 15 minutes before exiting
            ):
        """
        Process all invoice files as they appear in the S3 bucket (polled using SQS queue)

        Args:
            llm_provider: LLM provider name (e.g., 'openai', 'anthropic')
            llm_model: Name of the specific model to use
        """
        # Create a queue poller
        poller = QueuePollerFactory().create_poller("sqs", queue_url="https://sqs.us-east-1.amazonaws.com/************/piaccounting-for-processing")
        while poller.poll_uptime() < poll_timeout:
            print(f"[InvoiceController] Polling for messages. {poller.poll_uptime()} seconds since last poll...")
            messages = poller.poll()
            for message in messages:
                # Process the message
                print(f"[InvoiceController] Received message: {message["Body"]}")
                poller.delete(message)

                if "Records" not in message["Body"]:
                    print(f"[InvoiceController] Message does not seem to contain a 'Records' key. Ignoring.")
                    continue

                if "ObjectCreated:Put" not in message["Body"]["Records"][0]["eventName"]:
                    print(f"[InvoiceController] Message does not seem to be an 'ObjectCreated:Put' event. Ignoring.")
                    continue

                # get filename
                if "s3" in message["Body"]["Records"][0] and "object" in message["Body"]["Records"][0]["s3"]:
                    filename = unquote_plus(message["Body"]["Records"][0]["s3"]["object"]["key"]) # unquoting turns %20 into spaces, %2F into /, etc.
                    print(f"[InvoiceController] Processing file: {filename}")
                else:
                    print(f"[InvoiceController] Message does not seem to contain a filename, or the filename is not in the expected location. Ignoring.")
                    continue

                # get bucket name
                if "s3" in message["Body"]["Records"][0]:
                    bucket = message["Body"]["Records"][0]["s3"]["bucket"]["name"]
                    print(f"[InvoiceController] Processing file from bucket: {bucket}")
                else:
                    print(f"[InvoiceController] Message does not seem to contain a bucket name, or the bucket name is not in the expected location. Ignoring.")
                    continue

                # download file from S3 bucket
                s3_repo = S3BucketRepository()
                file = s3_repo.read(bucket, filename)
                file_content = file.read()

                with tempfile.NamedTemporaryFile(delete=False, suffix=".pdf") as tmp_file:
                    tmp_file.write(file_content)
                    tmp_file_path = tmp_file.name
                    tmp_file_stem = Path(tmp_file_path).stem

                with redirect_stdout(StringIO()) as f_stdout, redirect_stderr(StringIO()) as f_stderr:
                    try:
                        # Create output directory if it doesn't exist
                        os.makedirs(self.output_folder, exist_ok=True)

                        # Update state with file information
                        state = ProcessState(
                            input_folder=os.path.dirname(tmp_file_path),
                            output_folder=self.output_folder,
                            filename=os.path.basename(tmp_file_path),
                            llm_provider=llm_provider,
                            llm_model=llm_model,
                            chart_of_accounts=self.chart_of_accounts
                            #intent_memos=intent_memos,
                            #project_code_memos=project_code_memos
                        )

                        # Create graph
                        graph_wrapper = StateGraphWrapper()

                        # Add nodes
                        graph_wrapper.add_node("copy", copy_file_node)
                        graph_wrapper.add_node("google_ocr", google_vision_ocr_node)
                        graph_wrapper.add_node("invoice_type", invoice_type_classifier_node)
                        graph_wrapper.add_node("azure_ocr", azure_ocr_node)
                        graph_wrapper.add_node("compare_ocr", compare_ocr_lengths_node)
                        graph_wrapper.add_node("write_text", write_text_file_node)
                        graph_wrapper.add_node("detect_if_invoice", detect_if_invoice_node)
                        graph_wrapper.add_node("llm_parser", llm_parser_node)
                        graph_wrapper.add_node("amazon_textract", amazon_textract_node)
                        graph_wrapper.add_node("intent", intent_node)
                        graph_wrapper.add_node("project_codes", project_codes_node)
                        graph_wrapper.add_node("frequency", frequency_node)
                        graph_wrapper.add_node("post_bill", post_bill_node)
                        graph_wrapper.add_node("write_json", write_json_file_node)

                        # Add fixed edges
                        graph_wrapper.add_edge(START, "copy")
                        graph_wrapper.add_edge("copy", "google_ocr")
                        graph_wrapper.add_edge("google_ocr", "invoice_type")

                        # Add conditional edge with router function
                        graph_wrapper.add_conditional_edge("invoice_type", route_by_invoice_type)

                        # Define remaining path for ALPHAGREP invoices
                        graph_wrapper.add_edge("azure_ocr", "compare_ocr")
                        graph_wrapper.add_edge("compare_ocr", "write_text")

                        # Common path through LLM parser and enrichment nodes
                        graph_wrapper.add_edge("write_text", "detect_if_invoice")
                        graph_wrapper.add_conditional_edge("detect_if_invoice", route_by_invoice_detection)
                        graph_wrapper.add_conditional_edge(
                            "llm_parser", 
                            check_lineitems,
                            {
                                True: "amazon_textract",
                                False: "intent"
                            }
                        )
                        graph_wrapper.add_edge("amazon_textract", "write_text")
                        graph_wrapper.add_edge("intent", "project_codes")
                        graph_wrapper.add_edge("project_codes", "frequency")
                        graph_wrapper.add_edge("frequency", "post_bill")
                        graph_wrapper.add_edge("post_bill", "write_json")
                        graph_wrapper.add_edge("write_json", END)

                        # Get workflow graph for visualization
                        graph_wrapper.compile()
                        # Get workflow graph for visualization
                        try:
                            # Attempt to generate a workflow graph visualization.
                            # NOTE: The previous call to get_graph(xray=True) is removed since it is no longer available.
                            self.workflow = graph_wrapper.app.get_graph(xray=True).draw_mermaid_png()
                            # write it to src\assets\workflow.png
                            with open("src\\assets\\workflow.png", "wb") as f:
                                f.write(self.workflow)
                        except Exception as graph_error:
                            print(f"[InvoiceController] Error generating workflow graph: {graph_error}")
                            self.workflow = None

                        # Run the graph
                        final_state = graph_wrapper.run(state)

                        # Write to ledger
                        #ledger.create(final_state.parsed_data)

                        # Clean up temp file
                        os.unlink(tmp_file_path)
                        # if the invoice processed successfully, move the PDF to the "piaccounting-processed" bucket
                        if final_state.bill_posted and final_state.is_invoice:# if the invoice processed successfully, move the PDF to the "piaccounting-processed" bucket
                            s3_repo.create("piaccounting-processed", filename, FileLike(filename, file_content))
                            print(f"[InvoiceController] Moved {filename} to piaccounting-processed bucket")
                            # delete the old file
                            s3_repo.delete(bucket, filename)
                            print(f"[InvoiceController] Deleted {filename} from {bucket}")
                        # uncomment this when we've sufficiently improved the hygiene utility
                        elif not final_state.is_invoice: # send to piaccounting-for-client-review
                            s3_repo.create("piaccounting-for-client-review", filename, FileLike(filename, file_content))
                            print(f"[InvoiceController] Moved {filename} to piaccounting-for-client-review bucket")
                            # delete the old file
                            s3_repo.delete(bucket, filename)
                            print(f"[InvoiceController] Deleted {filename} from {bucket}")
                        else: # send to piaccounting-for-review-by-pillar
                            s3_repo.create("piaccounting-for-review-by-pillar", filename, FileLike(filename, file_content))
                            print(f"[InvoiceController] Moved {filename} to piaccounting-for-review-by-pillar bucket")
                            # delete the old file
                            s3_repo.delete(bucket, filename)
                            print(f"[InvoiceController] Deleted {filename} from {bucket}")
                    except Exception as e:
                        print(f"[InvoiceController] Error processing {filename}: {str(e)}")
                        import traceback
                        traceback.print_exc()

                    # upload log files to S3 bucket
                    stdout_log = FileLike(f"{tmp_file_stem}_stdout.log", f_stdout.getvalue().encode("utf-8"))
                    stderr_log = FileLike(f"{tmp_file_stem}_stderr.log", f_stderr.getvalue().encode("utf-8"))
                    s3_bucket = S3BucketRepository()
                    s3_bucket.create("pikernel-debug-results", f"{tmp_file_stem}_stdout.log", stdout_log)
                    s3_bucket.create("pikernel-debug-results", f"{tmp_file_stem}_stderr.log", stderr_log)
            
                    # print stdout and stderr
                    print("stdout:")
                    print(f_stdout.getvalue())
                    print("stderr:")
                    print(f_stderr.getvalue())
        
        print(f"[InvoiceController] No new invoices found after polling for {poller.poll_uptime()/60:.2f} minutes. Exiting...")
        return 0


    def run_with_llm(self, llm_provider: str, llm_model: str):
        """
        Legacy method for backward compatibility.
        Process all invoices in the input folder using the specified LLM.

        Args:
            llm_provider: LLM provider name
            llm_model: Model name to use
        """
        return self.process_invoices_from_folder(llm_provider, llm_model)

    def get_workflow_graph(self):
        """
        Get the current workflow graph visualization.

        Returns:
            bytes: PNG image data of the workflow graph or None if not available
        """
        return self.workflow
