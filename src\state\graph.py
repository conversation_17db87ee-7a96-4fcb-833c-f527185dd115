# Modify src/state/graph.py

from langgraph.graph import Graph, START, END
from src.state.state import ProcessState


# Modify src/state/graph.py

class StateGraphWrapper:
    def __init__(self):
        self.graph = Graph()
        self.app = None

    def add_node(self, name: str, node_callable):
        self.graph.add_node(name, node_callable)

    def add_edge(self, source: str, target: str):
        self.graph.add_edge(source, target)

    def add_conditional_edge(self, source: str, router_function, targets = None):
        """Add a conditional edge that routes based on the router function's output"""
        if targets is None:
            self.graph.add_conditional_edges(source, router_function)
        else:
            if type(targets) is not dict:
                raise ValueError("[add_conditional_edge] targets must be a dictionary")
            self.graph.add_conditional_edges(source, router_function, targets)

    def compile(self):
        self.app = self.graph.compile()

    def run(self, initial_state: ProcessState) -> ProcessState:
        initial_state_dict = initial_state.model_dump()
        final_state_dict = self.app.invoke(initial_state_dict)
        return ProcessState(**final_state_dict)