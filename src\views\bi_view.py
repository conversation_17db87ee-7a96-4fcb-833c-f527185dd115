import streamlit as st
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
from typing import Dict, List, Any
from plotly.graph_objects import Ohlc, Figure
from numpy import ndarray


class SunburstView:
    """
    View component for displaying expense distribution using a sunburst chart.
    Follows the MVC pattern as a View component.
    """

    def __init__(self, processed_data: List[Dict[str, Any]], sub_account_colors: Dict[str, str]):
        """
        Initialize with processed invoice data and color mappings.

        Args:
            processed_data: List of processed invoice data dictionaries
            sub_account_colors: Mapping of sub-account names to hex color codes
        """
        self.processed_data = processed_data
        self.sub_account_colors = sub_account_colors

    def render(self):
        """Renders the sunburst visualization in the Streamlit UI"""
        if not self.processed_data:
            st.info("No processed data available. Process invoices to view expense distribution.")
            return

        # Prepare data for sunburst chart
        chart_data = self._prepare_chart_data()

        positive_chart_data = chart_data['positive_chart']
        negative_chart_data = chart_data['negative_chart']

        if not positive_chart_data['labels'] and not negative_chart_data['labels']:
            st.warning("No line items found with sub-account information.")
            return

        # Create the sunburst chart for positive values
        if positive_chart_data['labels']:
            fig_positive = go.Figure(go.Sunburst(
                ids=positive_chart_data['ids'],
                labels=positive_chart_data['labels'],
                parents=positive_chart_data['parents'],
                values=positive_chart_data['values'],
                branchvalues='total',
                marker=dict(
                    colors=positive_chart_data['colors']
                ),
                textinfo='label+value',
                hovertemplate='<b>%{label}</b><br>Amount: $%{value:.2f}<br><extra></extra>'
            ))

            fig_positive.update_layout(
                title='Expenses',
                width=800,
                height=800,
                margin=dict(t=30, l=0, r=0, b=0)
            )

            # Display the chart
            st.plotly_chart(fig_positive, use_container_width=True)

            # Show supporting data
            with st.expander("View Expenses Data Table"):
                st.dataframe(positive_chart_data['dataframe'])

        # Create the sunburst chart for negative values
        if negative_chart_data['labels']:
            fig_negative = go.Figure(go.Sunburst(
                ids=negative_chart_data['ids'],
                labels=negative_chart_data['labels'],
                parents=negative_chart_data['parents'],
                values=negative_chart_data['values'],
                branchvalues='total',
                marker=dict(
                    colors=negative_chart_data['colors']
                ),
                textinfo='label+value',
                hovertemplate='<b>%{label}</b><br>Amount: $%{value:.2f}<br><extra></extra>'
            ))

            fig_negative.update_layout(
                title='Vendor Credits',
                width=800,
                height=800,
                margin=dict(t=30, l=0, r=0, b=0)
            )

            # Display the chart
            st.plotly_chart(fig_negative, use_container_width=True)

            # Show supporting data
            with st.expander("View Credits Data Table"):
                st.dataframe(negative_chart_data['dataframe'])

    def _prepare_chart_data(self):
        """Prepares data for the sunburst chart, separating negative line items."""
        # Extract line items with sub-account information
        all_line_items = []
        negative_line_items = []
        for data in self.processed_data:
            if 'parsed_data' in data and 'line_items' in data['parsed_data']:
                for item in data['parsed_data']['line_items']:
                    if 'sub_account' in item and 'total_price' in item:
                        amount = float(item['total_price'])
                        if amount < 0:
                            negative_line_items.append(item)
                        else:
                            all_line_items.append(item)

        # Handle case where no valid line items exist
        if not all_line_items and not negative_line_items:
            return {
                'positive_chart': {'ids': [], 'labels': [], 'parents': [], 'values': [], 'colors': [], 'dataframe': pd.DataFrame()},
                'negative_chart': {'ids': [], 'labels': [], 'parents': [], 'values': [], 'colors': [], 'dataframe': pd.DataFrame()}
            }

        def create_chart_data(line_items, is_negative=False):
            """Helper function to create chart data."""
            sub_account_totals = {}
            for item in line_items:
                sub_account = item.get('sub_account', 'Uncategorized')
                amount = abs(float(item.get('total_price', 0))) if is_negative else float(item.get('total_price', 0))

                if sub_account in sub_account_totals:
                    sub_account_totals[sub_account] += amount
                else:
                    sub_account_totals[sub_account] = amount

            # Create arrays for sunburst chart
            ids = ['root']
            labels = ['All Expenses (Negative)' if is_negative else 'All Expenses']
            parents = ['']
            values = [sum(sub_account_totals.values())]
            colors = ['#FFFFFF']  # Root node color

            # Add sub-account nodes
            for sub_account, total in sub_account_totals.items():
                ids.append(f'subacct_{sub_account}')
                labels.append(sub_account)
                parents.append('root')
                values.append(total)
                colors.append(self.sub_account_colors.get(sub_account, '#CCCCCC'))

            # Add individual items
            for i, item in enumerate(line_items):
                sub_account = item.get('sub_account', 'Uncategorized')
                description = item.get('description', f'Item {i + 1}')
                amount = abs(float(item.get('total_price', 0))) if is_negative else float(item.get('total_price', 0))

                # Truncate long descriptions
                short_desc = description[:40] + '...' if len(description) > 40 else description

                ids.append(f'item_{i}')
                labels.append(short_desc)
                parents.append(f'subacct_{sub_account}')
                values.append(amount)
                colors.append(self.sub_account_colors.get(sub_account, '#CCCCCC'))

            # Create dataframe for table view
            df_data = {
                'Description': [labels[i] for i in range(len(ids))],
                'Category': [parents[i] for i in range(len(ids))],
                'Amount': [values[i] for i in range(len(ids))]
            }
            df = pd.DataFrame(df_data)

            return {
                'ids': ids,
                'labels': labels,
                'parents': parents,
                'values': values,
                'colors': colors,
                'dataframe': df
            }

        # Create chart data for positive and negative line items
        positive_chart_data = create_chart_data(all_line_items)
        negative_chart_data = create_chart_data(negative_line_items, is_negative=True)

        return {
            'positive_chart': positive_chart_data,
            'negative_chart': negative_chart_data
        }

# this is only temporary until ledger.py is integrated with visualization_services.py
def generate_colors(expense_categories : ndarray) -> Dict[str, str]:
    """
    Generate colors for each expense category

    :param expense_categories: List of expense categories
    :ptype expense_categories: ndarray
    :return: Discrete color mapping for each category
    :rtype: Dict[str, str]
    """
    colors = px.colors.qualitative.Plotly
    return {category: colors[i % len(colors)] for i, category in enumerate(expense_categories)}

def create_histogram(ledger: pd.DataFrame):
    ledger_copy = ledger.copy()
    row_count = ledger_copy.shape[0]
    st.write("This histogram consists of a large ledger of sample data mixed with the currently selected invoices' data.")
    st.write(f"Total expense entries: {row_count}")
    tostr = lambda x: x.astype(int).astype(str)
    
    # generate columns for quarter and month. this will be used for grouping
    date_column = ledger_copy['date'].dt
    ledger_copy['quarter'] = tostr(date_column.year) + 'Q' + tostr(date_column.quarter)
    ledger_copy['month'] = date_column.month_name() + ' ' + tostr(date_column.year)

    # make sure the data is sorted by date
    sorted_ledger = ledger_copy.sort_values(by='date')

    # prompt to select the date range
    filter_by_quarter = st.checkbox("Filter period (quarter)", value=True)
    fig_b = None
    columns = st.columns(2)
    if filter_by_quarter:
        with columns[0]:
            from_quarter = st.selectbox("From", sorted_ledger['quarter'].unique())
        with columns[1]:
            to_quarter = st.selectbox("To", sorted_ledger['quarter'].unique())
        filtered_ledger = sorted_ledger[(sorted_ledger['quarter'] >= from_quarter) & (sorted_ledger['quarter'] <= to_quarter)]
    else:
        with columns[0]:
            from_quarter = st.selectbox("From", "(disabled)", disabled=True)
        with columns[1]:
            to_quarter = st.selectbox("To", "(disabled)", disabled=True)
        filtered_ledger = sorted_ledger
    
    # genereate colors for each expense category
    expense_categories = filtered_ledger['expense_category'].unique()
    colors = generate_colors(expense_categories)

    # plot graphs
    fig = px.histogram(
        filtered_ledger,
        x='month',
        y='total_price',
        histfunc='sum',
        nbins=3,
        category_orders={'month': sorted_ledger['month'].unique()},
        color='expense_category',
        color_discrete_map=colors,
        labels={'total_price': 'expenses', 'month': 'date'}
    )
    ## hide legend
    #fig.update_layout(showlegend=False)
    selection = st.plotly_chart(fig, on_select="rerun")
    # get the legend group
    if 'selection' in selection:
        selected_expense_categories = [point["legendgroup"] for point in selection["selection"]["points"]]
        filtered_ledger_prime = filtered_ledger[filtered_ledger['expense_category'].isin(selected_expense_categories)]
    else:
        filtered_ledger_prime = filtered_ledger

    if filter_by_quarter:
        column_count = 4
        by_expense_category = filtered_ledger_prime.groupby('expense_category')
        # filter dataframes where shape[0] < 2
        by_expense_category = list(filter(lambda x: x[1].shape[0] >= 2, by_expense_category))
        columns = st.columns(column_count)
        col = 0
        if len(by_expense_category) == 0:
            st.info("Averages for expense categories will be displayed here for a sufficiently large histogram selection.")
        for expense_category, df in by_expense_category:
            if df.shape[0] >= 2:
                fig_b = create_boxplot(df, from_quarter + "-" + to_quarter, colors[expense_category])
                with columns[col]:
                    st.write(f"**{expense_category}**")
                    st.plotly_chart(fig_b, key=expense_category)
                col = (col + 1) % column_count
    else:
        st.info("Averages for expense categories will be displayed here for a sufficiently large histogram selection.")


def create_boxplot(ledger_subset : pd.DataFrame, selected_quarter: str, color: str) -> Figure: 
    # get the min max and mean of total_price where ledget_subset['quarter'] == quarter
    min, max, mean = ledger_subset['total_price'].min(), ledger_subset['total_price'].max(), ledger_subset['total_price'].mean()
    #color = 'blue' # needs to be changes to something dynamic, presumably using src.models.visualization_services
    fig_b = go.Figure(data=go.Box(
        q1 = [mean],
        median = [mean],
        q3 = [mean],
        lowerfence = [min],
        upperfence = [max],
        x = [selected_quarter],
        marker_color = color,
        hoverinfo='y+name',
        name = 'expenses'
    ))
    fig_b.update_layout(
        yaxis_title='expenses',
        xaxis_title='period'
    )

    return fig_b

class BusinessIntelligenceView:
    """
    View component for displaying business intelligence visualizations.
    Follows the MVC pattern as a View component.
    """

    def __init__(self, processed_data: List[Dict[str, Any]], sub_account_colors: Dict[str, str], ledger_data: pd.DataFrame):
        """
        Initialize with processed invoice data and color mappings.

        Args:
            processed_data: List of processed invoice data dictionaries
            sub_account_colors: Mapping of sub-account names to hex color codes
        """
        self.processed_data = processed_data
        self.sub_account_colors = sub_account_colors
        self.ledger_data = ledger_data

    def render(self):
        """Renders business intelligence visualizations in the Streamlit UI"""
        if not self.processed_data:
            st.info("No processed data available. Process invoices to view business intelligence.")
            return

        # Prepare data for visualizations
        data = self._prepare_data()

        if data.empty:
            st.warning("No suitable data found for business intelligence visualizations.")
            return

        # Create tabs for different visualizations
        bi_tabs = st.tabs(["Time Series", "Category Analysis", "Supplier Analysis"])

        # Time Series tab
        with bi_tabs[0]:
            self._render_time_series()

        # Category Analysis tab
        with bi_tabs[1]:
            self._render_category_analysis(data)

        # Supplier Analysis tab
        with bi_tabs[2]:
            self._render_supplier_analysis(data)

    def _prepare_data(self):
        """Prepares data for BI visualizations"""
        # Collect all line items with dates
        all_items = []

        for data in self.processed_data:
            if 'parsed_data' not in data:
                continue

            parsed_data = data['parsed_data']

            # Get invoice details
            invoice_number = parsed_data.get('invoice_header', {}).get('invoice_number', 'Unknown')
            invoice_date = parsed_data.get('invoice_header', {}).get('invoice_date', 'Unknown')
            supplier = parsed_data.get('invoice_header', {}).get('supplier', {}).get('name', 'Unknown Supplier')

            # Process line items
            if 'line_items' in parsed_data:
                for item in parsed_data['line_items']:
                    all_items.append({
                        'Invoice': invoice_number,
                        'Date': invoice_date,
                        'Supplier': supplier,
                        'Description': item.get('description', 'Unknown'),
                        'Frequency': item.get('frequency', 'UNKNOWN'),
                        'Amount': float(item.get('total_price', 0)),
                        'Sub-Account': item.get('sub_account', 'Uncategorized')
                    })

        # Convert to DataFrame
        df = pd.DataFrame(all_items)

        # Convert date to datetime
        try:
            df['Date'] = pd.to_datetime(df['Date'])
            # Add month and year columns
            df['Month'] = df['Date'].dt.strftime('%Y-%m')
            df['Year'] = df['Date'].dt.year
        except:
            # If date conversion fails, use current date
            current_date = datetime.now()
            df['Date'] = current_date
            df['Month'] = current_date.strftime('%Y-%m')
            df['Year'] = current_date.year

        return df

    def _render_time_series(self):
        """Renders time series visualizations"""
        st.subheader("Expense Trends Over Time")

        create_histogram(self.ledger_data)

    def _render_category_analysis(self, data):
        """Renders category analysis visualizations"""
        st.subheader("Expense Analysis by Category")

        # load sunburst chart
        sunburst_view = SunburstView(self.processed_data, self.sub_account_colors)
        sunburst_view.render()

    def _render_supplier_analysis(self, data):
        """Renders supplier analysis visualizations"""
        st.subheader("Expense Analysis by Supplier")

        # Group by supplier and calculate totals
        supplier_data = data.groupby('Supplier')['Amount'].sum().reset_index()
        supplier_data = supplier_data.sort_values('Amount', ascending=False)

        # Limit to top 10 suppliers
        top_suppliers = supplier_data.head(10)

        # Create bar chart
        fig = px.bar(
            top_suppliers,
            x='Supplier',
            y='Amount',
            title='Top 10 Suppliers by Expense',
            labels={'Supplier': 'Supplier', 'Amount': 'Total Amount ($)'},
            color='Amount',
            color_continuous_scale=px.colors.sequential.Blues
        )

        st.plotly_chart(fig, use_container_width=True)

        # Supplier-Category breakdown
        supplier_category = data.groupby(['Supplier', 'Sub-Account'])['Amount'].sum().reset_index()
        supplier_category = supplier_category.sort_values('Amount', ascending=False)

        # Create heatmap
        pivot_table = supplier_category.pivot_table(
            values='Amount',
            index='Supplier',
            columns='Sub-Account',
            fill_value=0
        )

        # Sort the pivot table rows by total amount
        pivot_table['Total'] = pivot_table.sum(axis=1)
        pivot_table = pivot_table.sort_values('Total', ascending=False)
        pivot_table = pivot_table.drop(columns=['Total'])

        # Take top 10 suppliers
        pivot_table = pivot_table.head(10)

        fig = px.imshow(
            pivot_table,
            labels=dict(x="Category", y="Supplier", color="Amount"),
            x=pivot_table.columns,
            y=pivot_table.index,
            color_continuous_scale=px.colors.sequential.Blues,
            title="Supplier-Category Expense Heatmap"
        )

        fig.update_xaxes(tickangle=45)

        st.plotly_chart(fig, use_container_width=True)