import joblib
import numpy as np
import os
from abc import ABC, abstractmethod
from typing import Optional
from sklearn.base import BaseEstimator
from src.models.llm_factory import LLMFactory
from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import Str<PERSON>utputParser
from langchain_core.language_models.chat_models import BaseChatModel


class InvoiceDetector(ABC):
    """Interface for invoice detection strategies"""

    @abstractmethod
    def detect(self, text: str) -> bool:
        """Detect if a document is an invoice based on its text content"""
        pass


class KeywordInvoiceDetector(InvoiceDetector):
    """Detects invoices based on keyword presence"""

    def __init__(self, keywords=None, min_matches=3):
        self.keywords = keywords or [
            "invoice", "bill", "payment", "amount", "total",
            "due date", "invoice number", "tax", "subtotal", 
            "credit memo", "credit notice", "credit note"
        ]
        self.antikeywords = ["receipt", "statement", "email", "e-mail", "re:", "subject"]
        self.min_matches = min_matches

    def detect(self, text: str) -> bool:
        text_lower = text.lower()
        
        # Count invoice-related keywords
        keyword_count = sum(1 for word in self.keywords if word in text_lower)
        print(f"[KeywordInvoiceDetector] Invoice keyword matches: {keyword_count}/{self.min_matches}")

        # Count anti-keywords
        antikeyword_count = sum(1 for word in self.antikeywords if word in text_lower)
        print(f"[KeywordInvoiceDetector] Anti-keyword matches: {antikeyword_count}")
        if antikeyword_count > 0:
            return False
        
        # Detect based on keyword threshold
        return keyword_count >= self.min_matches


class LayoutInvoiceDetector(InvoiceDetector):
    """Detects invoices based on document layout patterns"""

    def detect(self, text: str) -> bool:
        # Check for common invoice layout patterns
        lines = text.split('\n')
        
        # Look for patterns like "Invoice #" followed by numbers
        invoice_number_pattern = any(
            "invoice" in line.lower() and "#" in line 
            for line in lines
        )
        
        # Look for patterns like "Total: $X.XX"
        total_pattern = any(
            ("total" in line.lower() and "$" in line) or
            ("amount due" in line.lower())
            for line in lines
        )
        
        # Look for date patterns
        date_pattern = any(
            "date" in line.lower() and any(month in line.lower() 
            for month in ["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"])
            for line in lines
        )
        
        # Document is an invoice if it matches at least 2 patterns
        matches = sum([invoice_number_pattern, total_pattern, date_pattern])
        print(f"[LayoutInvoiceDetector] Invoice layout matches: {matches}/2")
        
        return matches >= 2

class LLMInvoiceDetector(InvoiceDetector):
    """Detects invoices using an LLM"""

    def __init__(self, llm: BaseChatModel):
        self.llm = llm
        self.output_parser = StrOutputParser()
        self.prompt = self._create_prompt()
        self.chain = self.prompt | self.llm | self.output_parser

    def _create_prompt(self):
        return ChatPromptTemplate.from_messages([
            ("system", '''
            You are an expert at determining if a document is an invoice based on its text content.
            Instructions:
            1. Analyze the given text and determine if it represents an invoice.
            2. Only return 'true' or 'false', nothing else.
            '''),
            ("human", "Document text: {text}\n\nIs this an invoice? Return 'true' or 'false'.")
        ])

    def detect(self, text: str) -> bool:
        if not text or text.isspace():
            raise ValueError("Invalid input. Please provide the document text.")
        try:
            result = self.chain.invoke({"text": text})
            return result.lower() == "true"
        except Exception as e:
            raise ValueError(f"Error detecting invoice: {str(e)}")

class MachineLearningInvoiceDetector(InvoiceDetector):
    """Detects invoices using a trained ML model"""

    def __init__(self, model_path="src/assets/invoice_hygiene.pkl", keywords: Optional[list] = None):
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"Model file not found: {model_path}")
        
        self.model: BaseEstimator = joblib.load(model_path)
        self.keywords = keywords or [
            "invoice", "bill", "payment", "amount", "total",
            "due date", "invoice number", "tax", "subtotal", 
            "credit memo", "credit notice", "credit note", 
            "receipt", "statement", "email", "e-mail", "re:", "subject"
        ]

    def _extract_features(self, text: str) -> np.ndarray:
        text_lower = text.lower()
        return np.array([[text_lower.count(keyword) for keyword in self.keywords]])

    def detect(self, text: str) -> bool:
        if not text or text.isspace():
            raise ValueError("Invalid input. Please provide the document text.")

        features = self._extract_features(text)
        # zip nparray with keywords for debugging
        print(f"[MachineLearningInvoiceDetector] Features: {dict(zip(self.keywords, features[0]))}")
        prediction = self.model.predict(features)[0]
        print(f"[MachineLearningInvoiceDetector] Prediction: {prediction}")
        return bool(prediction)
