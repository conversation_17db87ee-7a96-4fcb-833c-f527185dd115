from src.models.invoice_detectors import InvoiceDetector, KeywordInvoiceDetector, LayoutInvoiceDetector, LLMInvoiceDetector, MachineLearningInvoiceDetector


class InvoiceDetectorFactory:
    """Factory for creating invoice detectors"""

    @staticmethod
    def create_detector(detector_type: str = "keyword", **kwargs) -> InvoiceDetector:
        """Create a detector based on type"""
        if detector_type == "keyword":
            keywords = kwargs.get("keywords")
            min_matches = kwargs.get("min_matches", 3)
            return KeywordInvoiceDetector(keywords=keywords, min_matches=min_matches)
        elif detector_type == "layout":
            return LayoutInvoiceDetector()
        elif detector_type == "combined":
            # Create a composite detector that combines multiple strategies
            detectors = []
            if kwargs.get("use_keyword", True):
                detectors.append(KeywordInvoiceDetector(
                    keywords=kwargs.get("keywords"),
                    min_matches=kwargs.get("min_matches", 3)
                ))
            if kwargs.get("use_layout", True):
                detectors.append(LayoutInvoiceDetector())
            
            # Create a composite detector that requires all detectors to agree
            return CompositeInvoiceDetector(detectors, require_all=kwargs.get("require_all", False))
        elif detector_type == "llm":
            llm = kwargs.get("llm")
            if not llm:
                raise ValueError("LLMInvoiceDetector requires an LLM instance")
            return LLMInvoiceDetector(llm)
        elif detector_type == "machine_learning":
            return MachineLearningInvoiceDetector()
        else:
            raise ValueError(f"Unsupported detector type: {detector_type}")


class CompositeInvoiceDetector(InvoiceDetector):
    """Combines multiple detectors with configurable logic"""
    
    def __init__(self, detectors, require_all=False):
        self.detectors = detectors
        self.require_all = require_all
    
    def detect(self, text: str) -> bool:
        if not self.detectors:
            return False
            
        results = [detector.detect(text) for detector in self.detectors]
        
        if self.require_all:
            # All detectors must agree it's an invoice
            return all(results)
        else:
            # Any detector can identify it as an invoice
            return any(results)