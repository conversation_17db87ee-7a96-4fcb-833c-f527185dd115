# ocr_result.py
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Tuple
import statistics
import pandas as pd

@dataclass
class TextBlock:
    text: str
    confidence: float
    block_type: str  # e.g., "LINE", "WORD", etc.
    page: int        # Page number for multi-page documents
    position: Dict[str, float]  # e.g., {"left": 0.1, "top": 0.2, "width": 0.3, "height": 0.1}
    provider_details: Dict[str, any] = field(default_factory=dict)
    children: List['TextBlock'] = field(default_factory=list)

class OCRResult:
    def __init__(self, text: str, text_blocks: List[TextBlock], provider: str):
        """
        Initialize an OCRResult with the full extracted text, a list of text blocks, and the provider's name.
        """
        self.text = text
        self.text_blocks = text_blocks
        self.provider = provider

    @property
    def average_confidence(self) -> float:
        """
        Calculate and return the average confidence of all text blocks.
        Returns 0.0 if no text blocks are present.
        """
        if not self.text_blocks:
            return 0.0
        return statistics.mean(block.confidence for block in self.text_blocks)

    @property
    def confidence_stats(self) -> Dict[str, float]:
        """
        Return a dictionary with statistical metrics (mean, median, standard deviation, minimum, and maximum)
        for the confidence scores of the text blocks.
        """
        if not self.text_blocks:
            return {}
        scores = [block.confidence for block in self.text_blocks]
        return {
            'mean': statistics.mean(scores),
            'median': statistics.median(scores),
            'std_dev': statistics.stdev(scores) if len(scores) > 1 else 0.0,
            'min': min(scores),
            'max': max(scores)
        }

    def get_low_confidence_blocks(self, threshold: float = 0.9) -> List[TextBlock]:
        """
        Return a list of text blocks with confidence below the given threshold.
        """
        return [block for block in self.text_blocks if block.confidence < threshold]

    def __str__(self) -> str:
        """
        Return a string representation of the OCRResult, including provider, block count, and confidence statistics.
        """
        stats = self.confidence_stats
        low_conf_blocks = self.get_low_confidence_blocks()
        output = [
            f"OCR Provider: {self.provider}",
            f"Number of text blocks: {len(self.text_blocks)}",
            "Confidence Statistics:",
            f"  Mean: {stats.get('mean', 0):.3f}",
            f"  Median: {stats.get('median', 0):.3f}",
            f"  Std Dev: {stats.get('std_dev', 0):.3f}",
            f"  Range: {stats.get('min', 0):.3f} - {stats.get('max', 0):.3f}"
        ]
        if low_conf_blocks:
            output.append("Low confidence blocks (<90%):")
            for block in low_conf_blocks:
                output.append(f"  '{block.text}' (confidence: {block.confidence:.3f}, page: {block.page})")
        return "\n".join(output)

    def extract_tables(self) -> List[pd.DataFrame]:
        """
        Extract tables from the OCR result by finding TABLE blocks and their CELL children.
        Returns a list of pandas DataFrames, one for each table found.
        """
        tables = []
        
        # Find all TABLE blocks
        table_blocks = [block for block in self.text_blocks if block.block_type == "TABLE"]
        
        for table_idx, table_block in enumerate(table_blocks):
            print(f"Processing table {table_idx+1} of {len(table_blocks)}")
            
            # Get all CELL children
            cell_blocks = [child for child in table_block.children if child.block_type == "CELL"]
            
            if not cell_blocks:
                print(f"No CELL blocks found in table {table_idx+1}")
                continue
                
            # Group cells by row index
            rows = {}
            for cell in cell_blocks:
                row_idx = cell.provider_details.get("RowIndex", 0)
                col_idx = cell.provider_details.get("ColumnIndex", 0)
                
                if row_idx not in rows:
                    rows[row_idx] = {}
                
                # Get the text from the cell's children (LINE or WORD blocks)
                cell_text = ""
                for child in cell.children:
                    if child.block_type in ["LINE", "WORD"]:
                        cell_text += child.text + " "
                
                rows[row_idx][col_idx] = cell_text.strip()
            
            # Convert the nested dictionary to a 2D array
            max_row = max(rows.keys()) if rows else 0
            max_col = max([max(row.keys()) for row in rows.values()]) if rows else 0
            
            # Create a 2D array filled with empty strings
            table_data = []
            for r in range(1, max_row + 1):  # Assuming 1-based indexing from AWS
                row_data = []
                for c in range(1, max_col + 1):  # Assuming 1-based indexing from AWS
                    cell_value = rows.get(r, {}).get(c, "")
                    row_data.append(cell_value)
                table_data.append(row_data)
            
            # Convert to DataFrame
            if table_data:
                # Use first row as header if it exists
                if len(table_data) > 0:
                    headers = table_data[0]
                    df = pd.DataFrame(table_data[1:], columns=headers)
                else:
                    df = pd.DataFrame(table_data)
                
                tables.append(df)
            
        print(f"[OCRResult] discovered {len(tables)} tables: \n{tables}")
        return tables
    
    def get_table_text_blocks(self) -> List[TextBlock]:
        """
        Return a list of all TABLE blocks in the OCR result.
        """
        return [block for block in self.text_blocks if block.block_type == "TABLE"]
