import streamlit as st
import json
from typing import Any

class ResultsView:
    """
    View component for displaying OCR results and confidence metrics.
    Follows the MVC pattern as a View component.
    """

    def __init__(self, state: Any):  # Accept any type (we expect a ProcessState or dict)
        self.state = state

    def render(self):
        # If self.state has a dict() method (e.g., a Pydantic model), use it; otherwise, assume it's already a dict.
        if hasattr(self.state, "dict"):
            state_dict = self.state.dict()
        else:
            state_dict = self.state

        ocr_results = state_dict.get('ocr_results', {})
        ocr_confidences = state_dict.get('ocr_confidences', {})

        if not ocr_results:
            st.info("No OCR results available. Process invoices to see results.")
            return

        # Filter out any empty keys (i.e. providers with empty labels)
        providers = [key for key in ocr_results.keys() if key.strip()]
        if not providers:
            st.info("No valid OCR provider results available.")
            return

        provider_tabs = st.tabs(providers)

        for i, provider in enumerate(providers):
            with provider_tabs[i]:
                text = ocr_results.get(provider, "")
                confidence = ocr_confidences.get(provider, {})

                # Display confidence metrics
                if confidence:
                    st.subheader("Confidence Metrics")
                    col1, col2, col3 = st.columns(3)

                    metrics = [
                        ("Mean", confidence.get('mean', 0)),
                        ("Median", confidence.get('median', 0)),
                        ("Standard Deviation", confidence.get('std_dev', 0)),
                        ("Minimum", confidence.get('min', 0)),
                        ("Maximum", confidence.get('max', 0))
                    ]

                    for idx, (label, value) in enumerate(metrics):
                        final_label = label if label.strip() else " "
                        with [col1, col2, col3][idx % 3]:
                            st.metric(final_label, f"{value:.3f}")

                # Use a non-empty label for the text area even if we hide it
                st.subheader("Extracted Text")
                st.text_area("Extracted Text", text, height=300, label_visibility="hidden")

                # Display parsed JSON if available
                parsed_jsons = state_dict.get('parsed_jsons', {})
                if provider in parsed_jsons:
                    st.subheader("Parsed JSON")
                    try:
                        parsed_json = parsed_jsons[provider]
                        if isinstance(parsed_json, str):
                            parsed_json = json.loads(parsed_json)
                        st.json(parsed_json)
                    except Exception as e:
                        st.error(f"Error displaying JSON: {str(e)}")
                elif 'parsed_data' in state_dict and state_dict['parsed_data']:
                    st.subheader("Parsed JSON")
                    st.json(state_dict['parsed_data'])
