#!/usr/bin/env python3
import sys
import os

# Add project root to Python path
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)
from langgraph.graph import START, END
from dotenv import load_dotenv

from src.state.state import ProcessState
from src.state.graph import StateGraphWrapper
from src.state.nodes import (
    copy_file_node,
    google_vision_ocr_node,
    azure_ocr_node,
    write_text_file_node,
    llm_parser_node,
    invoice_type_classifier_node,
    compare_ocr_lengths_node,
    route_by_invoice_type,
    intent_node,
    frequency_node,
    write_json_file_node
)
from src.models.chart_of_accounts import ChartOfAccounts

load_dotenv()


def route_by_invoice_type(state):
    """Route based on invoice type"""
    invoice_type = state.get("invoice_type", "UNKNOWN")

    if invoice_type == "ALPHAGREP":
        print(f"[route_by_invoice_type] ALPHAGREP invoice: routing to Azure OCR")
        return "azure_ocr"
    else:
        print(f"[route_by_invoice_type] REGULAR invoice: preparing Google OCR text for LLM")
        # For REGULAR invoices, set Google text as selected text directly
        state["selected_ocr_provider"] = "google"
        state["selected_ocr_text"] = state.get("ocr_results", {}).get("google", "")
        return "write_text"


def test_single_invoice_processing():
    # Hard-coded absolute folder (directory), not the PDF path
    input_folder = "/Users/<USER>/PycharmProjects/AlphaGrep/input_invoices"
    # The invoice file must actually exist at: /Users/<USER>/AlphaGrep/input_invoices/GuavaTech Invoice 1-28343 March 2025.pdf
    filename = "GuavaTech Invoice 1-28343 March 2025.pdf"

    # Hard-coded absolute output folder
    output_folder = "/Users/<USER>/PycharmProjects/AlphaGrep/single_test"
    os.makedirs(output_folder, exist_ok=True)

    # Initialize Chart of Accounts
    chart_of_accounts = ChartOfAccounts.create("Excel")

    # Build initial state
    state = ProcessState(
        input_folder=input_folder,
        output_folder=output_folder,
        filename=filename,
        llm_provider="openai",
        chart_of_accounts=chart_of_accounts
    )

    # Build the state graph
    graph_wrapper = StateGraphWrapper()

    # Add nodes
    graph_wrapper.add_node("copy", copy_file_node)
    graph_wrapper.add_node("google_ocr", google_vision_ocr_node)
    graph_wrapper.add_node("invoice_type", invoice_type_classifier_node)
    graph_wrapper.add_node("azure_ocr", azure_ocr_node)
    graph_wrapper.add_node("compare_ocr", compare_ocr_lengths_node)
    graph_wrapper.add_node("write_text", write_text_file_node)
    graph_wrapper.add_node("llm_parser", llm_parser_node)
    graph_wrapper.add_node("intent", intent_node)
    graph_wrapper.add_node("frequency", frequency_node)
    graph_wrapper.add_node("write_json", write_json_file_node)

    # Add fixed edges
    graph_wrapper.add_edge(START, "copy")
    graph_wrapper.add_edge("copy", "google_ocr")
    graph_wrapper.add_edge("google_ocr", "invoice_type")

    # Add conditional edge with router function
    graph_wrapper.add_conditional_edge("invoice_type", route_by_invoice_type)

    # Define remaining path for ALPHAGREP invoices
    graph_wrapper.add_edge("azure_ocr", "compare_ocr")
    graph_wrapper.add_edge("compare_ocr", "write_text")

    # Common path through LLM parser and enrichment nodes
    graph_wrapper.add_edge("write_text", "llm_parser")
    graph_wrapper.add_edge("llm_parser", "intent")
    graph_wrapper.add_edge("intent", "frequency")
    graph_wrapper.add_edge("frequency", "write_json")
    graph_wrapper.add_edge("write_json", END)

    # Run the graph
    final_state = graph_wrapper.run(state)

    print("[Test] Final state for", filename)
    print(final_state)

    # Print the path to the enriched JSON file
    if "enriched_json_path" in final_state:
        print(f"[Test] Enriched JSON saved to: {final_state['enriched_json_path']}")


if __name__ == "__main__":
    test_single_invoice_processing()