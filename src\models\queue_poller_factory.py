from src.models.queue_pollers import QueuePoller, SqsQueuePoller

class QueuePollerFactory:
    def __init__(self):
        self.pollers = {
            "sqs": SqsQueuePoller
        }

    def create_poller(self, poller_type: str, **kwargs) -> QueuePoller:
        if poller_type.lower() in self.pollers:
            return self.pollers[poller_type.lower()](**kwargs)
        else:
            raise ValueError(f"Unsupported poller type: {poller_type}")
