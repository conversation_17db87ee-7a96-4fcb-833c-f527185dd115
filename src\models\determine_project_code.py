from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
import json
import logging


class CodesParser:
    def __init__(self, llm, memos):
        self.llm = llm
        self.output_parser = StrOutputParser()
        self.memos = memos
        self.prompt = self._create_prompt()
        self.chain = self.prompt | self.llm | self.output_parser

    def _create_prompt(self):
        return ChatPromptTemplate.from_messages([
            ("system", '''You are an expert at determining which project codes, employee codes, and department codes 
             an invoice's line item should be posted to based on the line item's description and based on the organization's accounting instructions. 
            
            Here are specific accounting instructions for this organization to help you decide where this firm posts:

            {memos_str}

            The format of the JSON output should be as follows:
            {json_str}
             
            Instructions:
            1. Use your skill and judgement to determine the correct project codes, employee codes, and department codes for the invoice provided
            2. Only respond to input by providing a parseable a JSON with the project codes, employee codes, and department codes. No other data or text.
            3. If you don't have enough information to determine the correct codes, give each code an empty list.
            4. Please output the JSON in plaintext, without any markdown decorations or code blocks.
             
            Extract the information and format it according to this schema'''),
            ("human", "Line item description: {text}\n\n"
                      "Determine the best sub-account for this line item in an invoice.")
        ])

    def determine_project_codes(self, text: dict):
        if len(self.memos) == 0:
            return '{"project_codes": [], "employee_codes": [], "department_codes": []}'
        memos_str = "\n\n".join([f"Memo: {memo.text}" for memo in self.memos])  # Add memos to prompt

        #print(memos_str)

        json_str = """
            {
                "project_codes": ["ProjectCode1", "ProjectCode2"],
                "employee_codes": ["EmployeeCode1", "EmployeeCode2"],
                "department_codes": ["DepartmentCode1", "DepartmentCode2"]
            }
        """
        text = json.dumps(text)
        logging.debug(f"Processing invoice text: {text}...")
        if not text or text.isspace():
            raise ValueError("Invalid input. Please provide the invoice text.")
        try:
            result = self.chain.invoke({"text": text, "memos_str": memos_str, "json_str": json_str})
            logging.debug(f"Parsing result: {result}")
            return result
        except Exception as e:
            logging.error(f"Error processing invoice: {str(e)}")
            raise ValueError(f"Error processing invoice: {str(e)}")