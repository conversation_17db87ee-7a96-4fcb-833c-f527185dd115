import pandas as pd
import os
from typing import Dict, Any

# Ledger Repository Implementation
class LedgerRepository():
    def __init__(self):
        base_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'assets')
        file_path = os.path.join(base_path, 'ledger.xlsx')
        try:
            self.df = pd.read_excel(file_path)
        except FileNotFoundError:
            self.df = pd.DataFrame(columns=['total_price', 'date', 'due_date', 'supplier', 'expense_category'])
            self.df.to_excel(file_path, index=False)

    def read_all(self) -> pd.DataFrame:
        self._ensure_datetime_conversion()
        return self._filter_valid_rows(self.df)

    def read(self, criteria: Dict[str, Any]) -> pd.DataFrame:
        self._ensure_datetime_conversion()
        filtered_df = self.df
        for key, value in criteria.items():
            filtered_df = filtered_df[filtered_df[key] == value]
        return self._filter_valid_rows(filtered_df)

    def create(self, invoice: Dict[str, Any]) -> None:
        header = invoice['invoice_header']
        supplier = header['supplier']['name']
        date = header['invoice_date']
        due_date = header['due_date']

        for line_item in invoice['line_items']:
            expense_category = line_item['sub_account']
            total_price = line_item['total_price']

            new_row = {
                'total_price': total_price, 
                'date': date, 
                'due_date': due_date, 
                'supplier': supplier, 
                'expense_category': expense_category
            }
            self.df = pd.concat([self.df, pd.DataFrame([new_row])], ignore_index=True)

        file_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'assets', 'ledger.xlsx')
        self.df.to_excel(file_path, index=False)

    def update(self, entity: Dict[str, Any]) -> None:
        # Implementation as per specific update logic
        pass

    def delete(self, entity: Dict[str, Any]) -> None:
        # Implementation as per specific delete logic
        pass

    def _ensure_datetime_conversion(self):
        try: 
            self.df['date'].dt
            self.df['due_date'].dt
        except AttributeError:
            self.df['date'] = pd.to_datetime(self.df['date'], format='%Y-%m-%d', errors='coerce')
            self.df['due_date'] = pd.to_datetime(self.df['due_date'], format='%Y-%m-%d', errors='coerce')

    def _filter_valid_rows(self, df: pd.DataFrame) -> pd.DataFrame:
        return df.dropna(subset=['date'])