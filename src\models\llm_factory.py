
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic

# class that wraps another class and logs all function calls being executed 
class Wrapper:
    def __init__(self, wrapped_class):
        self.wrapped_class = wrapped_class

    def __getattr__(self, attr):
        original_func = getattr(self.wrapped_class, attr)

        def wrapper(*args, **kwargs):
            print(f"[OpenAIProvider] Calling function: {attr}")
            print(f"Arguments: {args}, {kwargs}")
            result = original_func(*args, **kwargs)
            print(f"Response: {result}")
            return result

        return wrapper

class OpenAIProvider:
    def create_llm(self, model_name, **kwargs):
        return ChatOpenAI(model_name=model_name, temperature=0, **kwargs)

class AnthropicProvider:
    def create_llm(self, model_name, **kwargs):
        return ChatAnthropic(model=model_name, temperature=0, **kwargs)

class GoogleProvider:
    def create_llm(self, model_name, **kwargs):
        return ChatGoogleGenerativeAI(model_name=model_name, temperature=0, **kwargs)

class LLMFactory:
    def __init__(self):
        self.providers = {
            "openai": OpenAIProvider(),
            "anthropic": AnthropicProvider(),
            "google": GoogleProvider(),
        }

    def create_llm(self, provider, model_name, **kwargs):
        print(f"Creating LLM with provider: {provider}, model: {model_name}")
        provider_lower = provider.lower()
        if provider_lower not in self.providers:
            raise Exception(f"Unsupported provider: {provider}")
        return self.providers[provider_lower].create_llm(model_name, **kwargs)
