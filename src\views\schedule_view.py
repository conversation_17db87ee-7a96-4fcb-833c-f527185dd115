import streamlit as st
import pandas as pd
from datetime import datetime, timedelta
import calendar
from typing import Dict, List, Any

# obtain the current month and year being processed
current_month = datetime.now().month
current_year = datetime.now().year
current_period = datetime(current_year, current_month, 1)

# days in month dictionary, alternates between 30 and 31 days for each month
# and accounts for leap years in February
days_in_month = {
    1: 31,
    2: 28 + (1 if current_year % 4 == 0 and (current_year % 100 != 0 or current_year % 400 == 0) else 0),
    3: 31,
    4: 30,
    5: 31,
    6: 30,
    7: 31,
    8: 31,
    9: 30,
    10: 31,
    11: 30,
    12: 31
}

class Prepayments_Schedule:
    def __init__(self, results):
        self.df = pd.DataFrame()
        for processed_data in results:
            parsed_data = processed_data["parsed_data"]
            vendor, date, ref, desc, periodicity, amount, start, end, dtform = [], [], [], [], [], [], [], [], []
            # get vendor, date, ref #
            # apply to each unique item desc, periodicity, amount
            for line_item in parsed_data["line_items"]:
                vendor.append(parsed_data["invoice_header"]["supplier"]["name"])
                date.append(parsed_data["invoice_header"]["invoice_date"])
                ref.append(parsed_data["invoice_header"]["invoice_number"])
                desc.append(line_item["description"])
                periodicity.append(line_item["frequency"])
                amount.append(line_item["total_price"])
                start.append(line_item.get('service_start', None))
                end.append(line_item.get('service_end', None))
                dtform.append(line_item.get('date_format', None))
            
            df = pd.DataFrame({
                "Vendor": vendor,
                "Date": date,
                "Reference": ref,
                "Description": desc,
                "Periodicity": periodicity,
                "Amount": amount,
                "Start": start,
                "End": end,
                "Date Format": dtform
            })

            self.df = pd.concat([self.df, df], ignore_index=True)

            def enddate_is_prepayment(x):
                if x['End']:
                    end_date = datetime.strptime(x['End'], x['Date Format'])
                    return (end_date > (current_period + timedelta(days=days_in_month[current_month]))\
                            or (x['Periodicity'] not in ['PREVIOUS_PERIOD', 'MONTHLY'])
                            )
                return False

            self.df = self.df[self.df[['End', 'Date Format', 'Periodicity']].apply(
                enddate_is_prepayment,
                axis=1
            )]

            def calculate_remaining(x):
                if x['End']:
                    end_date = datetime.strptime(x['End'], x['Date Format'])
                    if end_date > current_period:
                        remaining_months = (end_date.year - current_year) * 12 + end_date.month - current_month
                        total_months = 1 + (end_date.year - datetime.strptime(x['Start'], x['Date Format']).year) * 12 + end_date.month - datetime.strptime(x['Start'], x['Date Format']).month
                        return round(x['Amount'] * (remaining_months / total_months), 2)
                return 0

            # calculate remaining amounts
            if not self.df.empty:
                self.df['Remaining'] = self.df.apply(calculate_remaining, axis=1)

            print("[ScheduleView] Prepayments Schedule:")
            print(self.df)

    def render(self):
        st.subheader("Prepayments Schedule")
        # Remove the Date Format column before displaying
        display_df = self.df.drop(columns=["Date Format"], errors="ignore")
        st.dataframe(display_df)

class Accruals_Schedule:
    def __init__(self, results):
        self.df = pd.DataFrame()
        for processed_data in results:
            parsed_data = processed_data["parsed_data"]
            vendor, date, ref, desc, periodicity, amount, start, end, dtform = [], [], [], [], [], [], [], [], []
            # get vendor, date, ref #
            # apply to each unique item desc, periodicity, amount
            print(parsed_data)
            for line_item in parsed_data["line_items"]:
                vendor.append(parsed_data["invoice_header"]["supplier"]["name"])
                date.append(parsed_data["invoice_header"]["invoice_date"])
                ref.append(parsed_data["invoice_header"]["invoice_number"])
                desc.append(line_item["description"])
                periodicity.append(line_item["frequency"])
                amount.append(line_item["total_price"])
                start.append(line_item.get('service_start', None))
                end.append(line_item.get('service_end', None))
                dtform.append(line_item.get('date_format', None))
            
            df = pd.DataFrame({
                "Vendor": vendor,
                "Date": date,
                "Reference": ref,
                "Description": desc,
                "Periodicity": periodicity,
                "Amount": amount,
                "Start": start,
                "End": end,
                "Date Format": dtform
            })

            self.df = pd.concat([self.df, df], ignore_index=True)

            def startdate_is_accrual(x):
                if x['Start']:
                    start_date = datetime.strptime(x['Start'], x['Date Format'])
                    return (start_date < current_period)
                return False

            def calculate_accrued(x):
                if x['Start']:
                    start_date = datetime.strptime(x['Start'], x['Date Format'])
                    if start_date < current_period and start_date.year == current_year:
                        elapsed_months = (current_year - start_date.year) * 12 + current_month - start_date.month
                        total_months = 1 + (datetime.strptime(x['End'], x['Date Format']).year - start_date.year) * 12 + datetime.strptime(x['End'], x['Date Format']).month - start_date.month
                        return round(x['Amount'] * (elapsed_months / total_months), 2)
                    elif start_date < current_period and start_date.year < current_year:
                        return x['Amount']
                return 0

            # get only accrued values or services from prior month
            self.df = self.df[(self.df['Periodicity'] == 'PREVIOUS_PERIOD') | (self.df[['Start', 'Date Format']].apply(
                startdate_is_accrual,
                axis=1
            ))]

            # calculate accrued amounts
            if not self.df.empty:
                self.df['Accrued'] = self.df.apply(calculate_accrued, axis=1)
        
            print("[ScheduleView] Accruals Schedule:")
            print(self.df)

    def render(self):
        st.subheader("Accruals Schedule")
        # Remove the Date Format column before displaying
        display_df = self.df.drop(columns=["Date Format"], errors="ignore")
        st.dataframe(display_df)

#class ScheduleView:
#    """
#    View component for displaying payment schedules based on invoice frequency.
#    Follows the MVC pattern as a View component.
#    """
#
#    def __init__(self, processed_data: List[Dict[str, Any]]):
#        """
#        Initialize with processed invoice data.
#
#        Args:
#            processed_data: List of processed invoice data dictionaries
#        """
#        self.processed_data = processed_data
#
#    def render(self):
#        """Renders the payment schedule in the Streamlit UI"""
#        if not self.processed_data:
#            st.info("No processed data available. Process invoices to view payment schedules.")
#            return
#
#        # Prepare schedule data
#        schedule_data = self._prepare_schedule_data()
#
#        if schedule_data.empty:
#            st.warning("No recurring payments found in the processed invoices.")
#            return
#
#        # Display filters
#        col1, col2 = st.columns(2)
#        with col1:
#            selected_frequency = st.multiselect(
#                "Filter by Frequency",
#                options=schedule_data['Frequency'].unique(),
#                default=schedule_data['Frequency'].unique()
#            )
#
#        with col2:
#            selected_months = st.slider(
#                "Projection Period (Months)",
#                min_value=1,
#                max_value=24,
#                value=12
#            )
#
#        # Filter the data
#        filtered_data = schedule_data[schedule_data['Frequency'].isin(selected_frequency)]
#
#        # Show the schedule table
#        st.subheader("Payment Schedule")
#        st.dataframe(filtered_data)
#
#        # Generate and show projection
#        projection = self._generate_projection(filtered_data, selected_months)
#
#        st.subheader(f"Payment Projection ({selected_months} months)")
#        st.dataframe(projection)
#
#        # Show summary
#        self._display_summary(filtered_data, projection)
#
#    def _prepare_schedule_data(self):
#        """Prepares the schedule data from invoice line items"""
#        schedule_items = []
#
#        for data in self.processed_data:
#            if 'parsed_data' not in data:
#                continue
#
#            parsed_data = data['parsed_data']
#
#            # Get invoice details
#            invoice_number = parsed_data.get('invoice_header', {}).get('invoice_number', 'Unknown')
#            invoice_date = parsed_data.get('invoice_header', {}).get('invoice_date', 'Unknown')
#            supplier = parsed_data.get('invoice_header', {}).get('supplier', {}).get('name', 'Unknown Supplier')
#
#            # Process line items
#            if 'line_items' in parsed_data:
#                for item in parsed_data['line_items']:
#                    # Only include items with frequency information
#                    if 'frequency' in item and item['frequency'] not in ['', None, 'MONTHLY']:
#                        schedule_items.append({
#                            'Invoice': invoice_number,
#                            'Date': invoice_date,
#                            'Supplier': supplier,
#                            'Description': item.get('description', 'Unknown'),
#                            'Frequency': item.get('frequency', 'UNKNOWN'),
#                            'Amount': float(item.get('total_price', 0)),
#                            'Sub-Account': item.get('sub_account', 'Uncategorized')
#                        })
#
#        return pd.DataFrame(schedule_items)
#
#    def _generate_projection(self, schedule_data, months):
#        """Generates a payment projection for the specified number of months"""
#        if schedule_data.empty:
#            return pd.DataFrame()
#
#        today = datetime.now()
#        projection = []
#
#        # Frequency to month mapping
#        frequency_intervals = {
#            'MONTHLY': 1,
#            'QUARTERLY': 3,
#            'SEMI-ANNUAL': 6,
#            'ANNUAL': 12
#        }
#
#        # Generate projection for each item
#        for _, item in schedule_data.iterrows():
#            frequency = item['Frequency']
#            interval = frequency_intervals.get(frequency, 1)
#
#            # Start from current month
#            current_date = today
#
#            # Generate projection entries
#            for i in range(months):
#                # Skip months that don't match the frequency pattern
#                if (i % interval) != 0 and frequency != 'MONTHLY':
#                    continue
#
#                projection_date = (current_date + timedelta(days=30 * i)).strftime('%Y-%m')
#
#                projection.append({
#                    'Month': projection_date,
#                    'Supplier': item['Supplier'],
#                    'Description': item['Description'],
#                    'Frequency': frequency,
#                    'Amount': item['Amount'],
#                    'Sub-Account': item['Sub-Account']
#                })
#
#        # Convert to DataFrame and aggregate by month
#        df = pd.DataFrame(projection)
#        if df.empty:
#            return df
#
#        # Sort by month
#        df['Month'] = pd.to_datetime(df['Month'])
#        df = df.sort_values('Month')
#        df['Month'] = df['Month'].dt.strftime('%Y-%m')
#
#        return df
#
#    def _display_summary(self, schedule_data, projection):
#        """Displays summary statistics about the schedule"""
#        if schedule_data.empty or projection.empty:
#            return
#
#        st.subheader("Schedule Summary")
#
#        # Calculate metrics
#        total_recurring = schedule_data['Amount'].sum()
#
#        # Group by frequency and calculate totals
#        frequency_totals = schedule_data.groupby('Frequency')['Amount'].sum().reset_index()
#        frequency_totals = frequency_totals.sort_values('Amount', ascending=False)
#
#        # Calculate monthly equivalent for each frequency
#        monthly_equivalents = {
#            'MONTHLY': 1,
#            'QUARTERLY': 1 / 3,
#            'SEMI-ANNUAL': 1 / 6,
#            'ANNUAL': 1 / 12
#        }
#
#        total_monthly_equivalent = sum(
#            row['Amount'] * monthly_equivalents.get(row['Frequency'], 0)
#            for _, row in frequency_totals.iterrows()
#        )
#
#        # Display metrics
#        col1, col2 = st.columns(2)
#
#        with col1:
#            st.metric("Total Recurring Amount", f"${total_recurring:,.2f}")
#            st.metric("Monthly Equivalent", f"${total_monthly_equivalent:,.2f}")
#
#        with col2:
#            # Group projection by month and calculate monthly totals
#            monthly_totals = projection.groupby('Month')['Amount'].sum()
#            if not monthly_totals.empty:
#                avg_monthly = monthly_totals.mean()
#                max_monthly = monthly_totals.max()
#
#                st.metric("Average Monthly Payment", f"${avg_monthly:,.2f}")
#                st.metric("Maximum Monthly Payment", f"${max_monthly:,.2f}")
#
#        # Display frequency breakdown
#        st.write("Breakdown by Frequency")
#
#        # Create a DataFrame with monthly equivalents
#        breakdown = frequency_totals.copy()
#        breakdown['Monthly Equivalent'] = breakdown.apply(
#            lambda row: row['Amount'] * monthly_equivalents.get(row['Frequency'], 0),
#            axis=1
#        )
#
#        st.dataframe(breakdown)
