# src/models/determine_frequency.py
from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.runnables.base import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Literal, Optional, Tuple
from datetime import datetime
import re
import logging

PeriodStr = Literal["ANNUAL", "SEMI-ANNUAL", "QUARTERLY", "MONTHLY", "PREVIOUS_PERIOD"]
PeriodTuple = Tuple[PeriodStr, Optional[Tuple[datetime, datetime]]]

class InvoiceDateExtractor:
    def __init__(self, llm: BaseChatModel):
        """
        Initializes the InvoiceDateExtractor with a given LLM.
        """
        self.llm = llm
        self.output_parser = StrOutputParser()
        self.prompt_template = self._extract_date_prompt()
        self.chain = self.prompt_template | self.llm | self.output_parser 
    
    def _extract_date_prompt(self) -> ChatPromptTemplate:
        """
        Creates a ChatPromptTemplate asking the LLM to extract start and end dates with strftime/strptime format codes.
        """
        return ChatPromptTemplate.from_template(
            """
            Extract the start and/or end date from the following invoice service period text:
            
            "{invoice_lineitem}"
            
            Provide the extracted date(s) in the format:
            - Start Date: <date> (<strftime format>)
            - End Date: <date> (<strftime format>)
            
            Ensure that the format follows Python's strftime/strptime format codes.
            Here are some examples of correct formats:
            - 'January 15, 2024' -> 'Start Date: January 15, 2024 (%B %d, %Y)'
            - '15/01/24' -> 'Start Date: 15/01/24 (%d/%m/%y)'
            - '2024-01-15' -> 'Start Date: 2024-01-15 (%Y-%m-%d)'
            - '01/15/24' -> 'Start Date: 01/15/24 (%m/%d/%y)'
            
            If only one date is present, return just that one. If no date is found, respond with 'None'.
            Do not change the format of the date. Make sure the date format matches the strftime/strptime format.
            """
        )
    
    def get_dates(self, invoice_lineitem: str) -> Optional[Tuple[Optional[Tuple[str, str]], Optional[Tuple[str, str]]]]:
        """
        Extracts the start and end dates along with their strftime/strptime format codes from an invoice line item.
        Returns a tuple (start_date, end_date) where each is a tuple (date_string, format_string).
        """
        response = self.chain.invoke({"invoice_lineitem": invoice_lineitem})
        
        # Parsing the LLM output
        start_date_match = re.search(r"Start Date: (.+?) \((.+?)\)", response)
        end_date_match = re.search(r"End Date: (.+?) \((.+?)\)", response)
        
        start_date = (start_date_match.group(1), start_date_match.group(2)) if start_date_match else None
        end_date = (end_date_match.group(1), end_date_match.group(2)) if end_date_match else None
        
        return start_date, end_date

class PeriodicityAnalyzer:
    def __init__(self, llm: BaseChatModel):
        """
        Initializes the PeriodicityAnalyzer with a given LLM.
        """
        self.llm = llm
        self.output_parser = StrOutputParser()
        self.prompt = self._create_prompt()
        self.chain = self.prompt | self.llm | self.output_parser
    
    def _create_prompt(self) -> ChatPromptTemplate:
        """
        Creates a ChatPromptTemplate for analyzing periodicity in invoice line items.
        """
        return ChatPromptTemplate.from_messages([
            ("system", """
            Periodicity Analyzer for Invoiced Line Items
            
            Role: You are an AI tool designed to analyze invoiced line items and determine whether they contain useful information regarding their periodicity. Your task is to assess whether a line item explicitly indicates that it is not a monthly expense (e.g., annual, quarterly, semi-annual) or if it lacks such information and is assumed to be monthly.
            
            Instructions:
            1. Return true if the line item contains clear indicators that it is an expense occurring on a period longer than a month (e.g., annual, quarterly, or semi-annual).
               - Example indicators:
                 - Terms such as "annual fee," "quarterly tax," "billed semi-annually," "yearly service charge."
                 - Expenses explicitly associated with a tax period (e.g., annual licensing fees, quarterly tax payments).
            
            2. Return false if the line item does not explicitly indicate a periodicity beyond monthly, meaning:
                - It is assumed to be a monthly expense (e.g., "accounting expense," "utility bill," "software subscription").
                - It refers to warranties or guarantees, which may cover multiple months but do not imply a recurring charge at quarterly or annual intervals.
            
            Edge Cases to Consider:
            - If a service period is a prior period (e.g., "last month's service"), return true, since it will need to be classified as PREVIOUS_PERIOD.
            - Ignore warranties and guarantees, even if they span multiple months. These should be classified as false unless they have explicit periodicity information.
            - If a line item contains ambiguous terms without a clear periodicity indicator, default to false (monthly).
            - Consider variations in phrasing (e.g., "billed annually" = "annual fee", "every three months" = "quarterly").
            
            Output:
            - Return a boolean value (true or false) based on the above logic. No explanations or additional text should be included in the response.
            """),
            ("human", "Line item description: {text}\n\nReturn 'true' or 'false' based on whether it suggests a period longer than monthly.")
        ])
    
    def find_service_period(self, text: str) -> bool:
        """
        Determines if a given invoice line item suggests a period longer than monthly.
        """
        if not text or text.isspace():
            raise ValueError("Invalid input. Please provide the line item description.")
        try:
            result = self.chain.run(text=text)
            return result.lower() == "true"
        except Exception as e:
            raise ValueError(f"Error analyzing periodicity of invoice line items: {str(e)}")

#class FrequencyParser:
#    """
#    Uses an LLM to determine the frequency (periodicity) of invoice line items
#    based on their descriptions.
#    """
#
#    def __init__(self, llm):
#        """
#        Initialize the FrequencyParser with an LLM.
#
#        Args:
#            llm: A language model instance
#        """
#        self.llm = llm
#        self.output_parser = StrOutputParser()
#        self.prompt = self._create_prompt()
#        self.chain = self.prompt | self.llm | self.output_parser
#
#    def _create_prompt(self):
#        """
#        Create a prompt that instructs the LLM how to determine payment frequency.
#
#        Returns:
#            ChatPromptTemplate: A formatted prompt template for the LLM
#        """
#        return ChatPromptTemplate.from_messages([
#            ("system", '''You are an expert at determining the frequency of invoice line items based on their description.
#            The possible frequencies are:
#            - ANNUAL: for expenses that occur once a year
#            - SEMI-ANNUAL: for expenses that occur twice a year
#            - QUARTERLY: for expenses that occur four times a year
#            - MONTHLY: for non-recurring expense or expenses that occur once a month
#
#            Instructions:
#            1. Analyze the given description and determine the most likely frequency.
#            2. Only return one of the above frequency labels, nothing else.
#            3. If unsure, default to MONTHLY.'''),
#            ("human", "Line item description: {text}\n\n"
#                      "Determine the frequency for this line item.")
#        ])
#
#    def determine_frequency(self, text: str) -> str:
#        """
#        Determine the frequency for a given line item description.
#
#        Args:
#            text (str): The description of the invoice line item
#
#        Returns:
#            str: The determined frequency (ANNUAL, SEMI-ANNUAL, QUARTERLY, or MONTHLY)
#
#        Raises:
#            ValueError: If the input is invalid or processing fails
#        """
#        logging.debug(f"Determining frequency for: {text[:100]}...")
#        if not text or text.isspace():
#            raise ValueError("Invalid input. Please provide the line item description.")
#        try:
#            result = self.chain.invoke({"text": text})
#            logging.debug(f"Determined frequency: {result}")
#            return result
#        except Exception as e:
#            logging.error(f"Error determining frequency: {str(e)}")
#            raise ValueError(f"Error determining frequency: {str(e)}")
class FrequencyParser:
    def __init__(self, llm: BaseChatModel):
        """
        Initializes the FrequencyParser with a given LLM.
        """
        self.llm = llm
        self.output_parser = StrOutputParser()
        self.invoice_date_extractor = InvoiceDateExtractor(llm)
        
        self.frequency_prompt = self._create_frequency_prompt()
        self.frequency_chain = self.frequency_prompt | self.llm | self.output_parser
        
        self.decision_prompt = PeriodicityAnalyzer(llm)._create_prompt()
        self.decision_chain = self.decision_prompt | self.llm | self.output_parser
        
        def router_lambda(inputs: dict) -> str:
            decision_result = self.decision_chain.invoke(inputs).strip().lower()
            if decision_result == "true":
                return self.frequency_chain.invoke(inputs)
            return "MONTHLY"
        
        self.chain = RunnableLambda(func=router_lambda)
    
    def _create_frequency_prompt(self) -> ChatPromptTemplate:
        """
        Creates the prompt for determining the frequency label.
        """
        return ChatPromptTemplate.from_messages([
            ("system", """
            You are an expert at determining the frequency of invoice line items based on their description.
            The possible frequencies are:
            - ANNUAL: for expenses that occur once a year
            - SEMI-ANNUAL: for expenses that occur twice a year
            - QUARTERLY: for expenses that occur four times a year
            - MONTHLY: for non-recurring expense or expenses that occur once a month
            - PREVIOUS_PERIOD: for expenses that occurred before the current period
            
            Instructions:
            1. Analyze the given description and determine the most likely frequency.
            2. If the description references a period longer than one month, return the appropriate frequency.
            3. Ignore any frequency descriptions related to warranties, guarantees, or similar terms.
            4. Only return one of the above frequency labels, nothing else.
            5. If the service period is before the invoice date, return PREVIOUS_PERIOD.
            6. If unsure, default to MONTHLY.
            """),
            ("human", "Line item description: {text}\n\nDetermine the frequency for this line item.")
        ])
    
    def determine_frequency(self, text: str, invoice_date: str) -> str:
        """
        Determines the frequency of a given invoice line item.
        """
        if not text or text.isspace():
            raise ValueError("Invalid input. Please provide the line item description.")
        try:
            result = self.chain.invoke({"text": text, "invoice_date": invoice_date})
            return result
        except Exception as e:
            raise ValueError(f"Error determining frequency: {str(e)}")