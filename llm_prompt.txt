===== PROMPT SENT TO LLM =====
System: You are an expert at extracting information from invoices. Extract the following details from the given invoice text and format the output according to the provided JSON schema. It is CRITICAL that you ONLY use the exact keys and structure specified in the schema. Do not add any additional fields or modify the structure in any way. If information is missing or not applicable, use null values or empty strings/arrays as appropriate.

IMPORTANT: For every object in the schema, include ALL child properties, even if their values are null. For example, if an 'address' object is missing, it should still be included as: {"street": null, "city": null, "state": null, "zip_code": null, "country": null}.

Your response must ONLY contain valid JSON with no additional text before or after. Do not include explanations, do not use markdown formatting, just provide the JSON object.

Here is the JSON schema to follow:
{
  "type": "object",
  "properties": {
    "invoice_header": {
      "type": "object",
      "properties": {
        "invoice_number": {
          "type": [
            "string",
            "null"
          ]
        },
        "invoice_date": {
          "type": [
            "string",
            "null"
          ]
        },
        "due_date": {
          "type": [
            "string",
            "null"
          ]
        },
        "supplier": {
          "type": [
            "object",
            "null"
          ],
          "properties": {
            "name": {
              "type": [
                "string",
                "null"
              ]
            },
            "address": {
              "type": [
                "object",
                "null"
              ],
              "properties": {
                "street": {
                  "type": [
                    "string",
                    "null"
                  ]
                },
                "city": {
                  "type": [
                    "string",
                    "null"
                  ]
                },
                "state": {
                  "type": [
                    "string",
                    "null"
                  ]
                },
                "zip_code": {
                  "type": [
                    "string",
                    "null"
                  ]
                },
                "country": {
                  "type": [
                    "string",
                    "null"
                  ]
                }
              }
            },
            "contact": {
              "type": [
                "object",
                "null"
              ],
              "properties": {
                "name": {
                  "type": [
                    "string",
                    "null"
                  ]
                },
                "email": {
                  "type": [
                    "string",
                    "null"
                  ]
                },
                "phone": {
                  "type": [
                    "string",
                    "null"
                  ]
                }
              }
            }
          }
        },
        "supplier_type": {
          "type": [
            "object",
            "null"
          ],
          "properties": {
            "supplier_category": {
              "type": [
                "string",
                "null"
              ]
            }
          }
        },
        "customer": {
          "type": [
            "object",
            "null"
          ],
          "properties": {
            "name": {
              "type": [
                "string",
                "null"
              ]
            },
            "address": {
              "type": [
                "object",
                "null"
              ],
              "properties": {
                "street": {
                  "type": [
                    "string",
                    "null"
                  ]
                },
                "city": {
                  "type": [
                    "string",
                    "null"
                  ]
                },
                "state": {
                  "type": [
                    "string",
                    "null"
                  ]
                },
                "zip_code": {
                  "type": [
                    "string",
                    "null"
                  ]
                },
                "country": {
                  "type": [
                    "string",
                    "null"
                  ]
                }
              }
            },
            "contact": {
              "type": [
                "object",
                "null"
              ],
              "properties": {
                "name": {
                  "type": [
                    "string",
                    "null"
                  ]
                },
                "email": {
                  "type": [
                    "string",
                    "null"
                  ]
                },
                "phone": {
                  "type": [
                    "string",
                    "null"
                  ]
                }
              }
            }
          }
        },
        "currency": {
          "type": [
            "string",
            "null"
          ]
        },
        "var_total": {
          "type": [
            "number",
            "null"
          ]
        },
        "sales_tax_total": {
          "type": [
            "number",
            "null"
          ]
        },
        "total_amount": {
          "type": [
            "number",
            "null"
          ]
        }
      }
    },
    "line_items": {
      "type": [
        "array",
        "null"
      ],
      "items": {
        "type": "object",
        "properties": {
          "date": {
            "type": [
              "string",
              "null"
            ]
          },
          "description": {
            "type": [
              "string",
              "null"
            ]
          },
          "quantity": {
            "type": [
              "number",
              "null"
            ]
          },
          "unit_price": {
            "type": [
              "number",
              "null"
            ]
          },
          "total_price": {
            "type": [
              "number",
              "null"
            ]
          },
          "sub_account": {
            "type": [
              "string",
              "null"
            ]
          },
          "account_id": {
            "type": [
              "number",
              "null"
            ]
          },
          "frequency": {
            "type": [
              "string",
              "null"
            ],
            "enum": [
              "SINGLE",
              "ANNUAL",
              "SEMI-ANNUAL",
              "QUARTERLY",
              "MONTHLY",
              null
            ]
          },
          "department_codes": {
            "type": [
              "array",
              "null"
            ],
            "items": {
              "type": "object",
              "properties": {
                "department_code": {
                  "type": [
                    "string",
                    "null"
                  ]
                }
              }
            }
          },
          "employee_codes": {
            "type": [
              "array",
              "null"
            ],
            "items": {
              "type": "object",
              "properties": {
                "employee_code": {
                  "type": [
                    "string",
                    "null"
                  ]
                }
              }
            }
          },
          "project_codes": {
            "type": [
              "array",
              "null"
            ],
            "items": {
              "type": "object",
              "properties": {
                "project_code": {
                  "type": [
                    "string",
                    "null"
                  ]
                }
              }
            }
          }
        }
      }
    },
    "supplier_bank_details": {
      "type": [
        "array",
        "null"
      ],
      "items": {
        "type": "object",
        "properties": {
          "bank_account_name": {
            "type": [
              "string",
              "null"
            ]
          },
          "bank_account_number": {
            "type": [
              "string",
              "null"
            ]
          },
          "bank_iban": {
            "type": [
              "string",
              "null"
            ]
          },
          "bank_swift": {
            "type": [
              "string",
              "null"
            ]
          },
          "bank_address": {
            "type": [
              "string",
              "null"
            ]
          }
        }
      }
    },
    "supplier_corp_details": {
      "type": [
        "array",
        "null"
      ],
      "items": {
        "type": "object",
        "properties": {
          "supplier_vat_number": {
            "type": [
              "string",
              "null"
            ]
          },
          "supplier_sales_tax_number": {
            "type": [
              "string",
              "null"
            ]
          },
          "supplier_company_registration_number": {
            "type": [
              "string",
              "null"
            ]
          },
          "supplier_registration_jurisdiction": {
            "type": [
              "string",
              "null"
            ]
          },
          "supplier_registered_address": {
            "type": [
              "string",
              "null"
            ]
          }
        }
      }
    },
    "accounting_app_posting": {
      "type": [
        "array",
        "null"
      ],
      "items": {
        "type": "object",
        "properties": {
          "app_bill_id": {
            "type": [
              "string",
              "null"
            ]
          },
          "app_audit_id": {
            "type": [
              "string",
              "null"
            ]
          },
          "app_posting_datetime_stamp": {
            "type": [
              "string",
              "null"
            ]
          },
          "invoice_filename": {
            "type": [
              "string",
              "null"
            ]
          },
          "posting_username": {
            "type": [
              "string",
              "null"
            ]
          },
          "username": {
            "type": [
              "string",
              "null"
            ]
          }
        }
      }
    },
    "graphdb_posting": {
      "type": [
        "array",
        "null"
      ],
      "items": {
        "type": "object",
        "properties": {
          "graphdb_id": {
            "type": [
              "string",
              "null"
            ]
          },
          "graphdb_datetime_stamp": {
            "type": [
              "string",
              "null"
            ]
          },
          "invoice_filename": {
            "type": [
              "string",
              "null"
            ]
          }
        }
      }
    }
  }
}

Human: Invoice text:
CONLINE
AW Mountain Services
201 The Chase
Chiltern Road
Watford W1 2DY
01347 701 0101
Invoice #1010
01/10/2024
BILLING ADDRESS
Pelagus Investment Services
24 Christopher
St
London
PAYMENT INFORMATION
30-days of invoice date
DESCRIPTION
Data hosting
DELIVERY ADDRESS
DELIVERY METHOD
Email
Units
PRICE/Unit
SUBTOTAL
4
£2,500.00
£10,000.00
Programming service on lambda functions
30
£500.00
£15,000.00
Market Data subscription 1/10/2024-
31/12/2024
3
£25,000.00
£75,000.00
Alteryx License 2025
7
£12,00.00
£84,000.00
Consulting on new data hierarchy
£12,500.00
Total
£12,500.00
£196,500.00
DUE DATE 31/10/2024
Additional comments:
VAT 20%
39,300.00
Total Amount
£235,800.00
Terms and Conditions:
Thank you for your business
VAT Registration Number: GB785369011

--- TABLE 1 ---

PAYMENT INFORMATION,DELIVERY METHOD

30-days of invoice date,Email



--- TABLE 2 ---

DESCRIPTION,Units,PRICE/Unit,SUBTOTAL

Data hosting,4,"£2,500.00","£10,000.00"

Programming service on lambda functions,30,£500.00,"£15,000.00"

Market Data subscription 1/10/2024- 31/12/2024,3,"£25,000.00","£75,000.00"

Alteryx License 2025,7,"£12,00.00","£84,000.00"

Consulting on new data hierarchy,,"£12,500.00","£12,500.00"

,,Total,"£196,500.00"

,,VAT 20%,"39,300.00"

,,Total Amount,"£235,800.00"



Extract the information and format it according to the provided schema. Return ONLY the JSON object.
===============================
