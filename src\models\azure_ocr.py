import os
import io
import time
from pathlib import Path
from pdf2image import convert_from_path
from azure.cognitiveservices.vision.computervision import ComputerVisionClient
from msrest.authentication import CognitiveServicesCredentials
from src.models.ocr_result import OCRResult, TextBlock


class AzureOCR:
    def __init__(self, endpoint: str = None, subscription_key: str = None):
        self.endpoint = endpoint or os.getenv("AZURE_ENDPOINT")
        self.subscription_key = subscription_key or os.getenv("AZURE_SUBSCRIPTION_KEY")
        if not self.endpoint or not self.subscription_key:
            raise ValueError("Azure endpoint and subscription key must be provided for OCR processing")
        print(f"[AzureOCR] Using endpoint: {self.endpoint}")
        print(f"[AzureOCR] Using subscription key: {self.subscription_key}")
        self.client = ComputerVisionClient(
            self.endpoint,
            CognitiveServicesCredentials(self.subscription_key)
        )
        print("[AzureOCR] Initialized ComputerVisionClient.")

    def extract_text(self, file_path: str) -> OCRResult:
        from pdf2image import convert_from_path  # ensure local import if needed
        path = os.path.join(file_path)
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")

        if file_path.lower().endswith(".pdf"):
            print(f"[AzureOCR] Converting PDF to images for {file_path}")
            try:
                images = convert_from_path(file_path)
                print(f"[AzureOCR] Converted PDF to {len(images)} images.")
            except Exception as e:
                raise Exception(f"Error converting PDF to images: {e}")

            all_text_blocks = []
            all_page_text = []
            for idx, image in enumerate(images):
                page_num = idx + 1
                print(f"[AzureOCR] Processing page {page_num}/{len(images)}")
                # Read image bytes
                img_byte_arr = io.BytesIO()
                image.save(img_byte_arr, format="JPEG")
                image_data = img_byte_arr.getvalue()
                try:
                    print("[AzureOCR] Calling read_in_stream on ComputerVisionClient.")
                    read_response = self.client.read_in_stream(
                        image=io.BytesIO(image_data),
                        raw=True
                    )
                    if read_response is None or not hasattr(read_response, "headers"):
                        raise Exception("No valid response received from Azure OCR API")
                    operation_location = read_response.headers.get("Operation-Location")
                    if not operation_location:
                        raise Exception("Operation-Location header not found in Azure OCR response")
                    operation_id = operation_location.split('/')[-1]
                    for attempt in range(30):
                        result = self.client.get_read_result(operation_id)
                        print(f"[AzureOCR] Attempt {attempt+1}: status = {result.status}")
                        if result.status not in ['notStarted', 'running']:
                            break
                        time.sleep(1)
                    if result.status != 'succeeded':
                        raise Exception(f"Azure OCR read operation failed with status: {result.status}")
                    # For simplicity, concatenate the text from all pages.
                    page_text = "\n".join([line.text for page_result in result.analyze_result.read_results for line in page_result.lines])
                    all_page_text.append(page_text)
                except Exception as e:
                    print(f"[AzureOCR] Error processing page {page_num}: {e}")
                    # Continue processing remaining pages
                    all_page_text.append("")
            final_text = "\n".join(all_page_text).strip()
            # Return an OCRResult even if text is empty.
            return OCRResult(final_text, all_text_blocks, provider="azure")
        else:
            # For image files
            with open(file_path, "rb") as f:
                image_data = f.read()
            try:
                print(f"[AzureOCR] Processing image file {file_path} directly.")
                read_response = self.client.read_in_stream(
                    image=io.BytesIO(image_data),
                    raw=True
                )
                if read_response is None or not hasattr(read_response, "headers"):
                    raise Exception("No valid response received from Azure OCR API")
                operation_location = read_response.headers.get("Operation-Location")
                if not operation_location:
                    raise Exception("Operation-Location header missing in Azure OCR response")
                operation_id = operation_location.split('/')[-1]
                for attempt in range(30):
                    result = self.client.get_read_result(operation_id)
                    print(f"[AzureOCR] Attempt {attempt+1}: status = {result.status}")
                    if result.status not in ['notStarted', 'running']:
                        break
                    time.sleep(1)
                if result.status != 'succeeded':
                    raise Exception(f"Azure OCR read operation failed with status: {result.status}")
                final_text = "\n".join([line.text for page_result in result.analyze_result.read_results for line in page_result.lines])
                return OCRResult(final_text.strip(), [], provider="azure")
            except Exception as e:
                raise Exception(f"AzureOCR processing error: {e}")


    def _read_image(self, image_data: bytes, page_number: int) -> str:
        # Use Azure's read_in_stream API
        read_response = self.client.read_in_stream(io.BytesIO(image_data), language="en")
        operation_location = read_response.headers.get("Operation-Location")
        if not operation_location:
            raise Exception("No operation location returned from Azure OCR")
        operation_id = operation_location.split("/")[-1]
        import time
        for attempt in range(10):
            result = self.client.get_read_result(operation_id)
            if result.status not in ["notStarted", "running"]:
                break
            time.sleep(1)
        if result.status == "succeeded":
            page_text = ""
            for page in result.analyze_result.read_results:
                for line in page.lines:
                    page_text += line.text + "\n"
            return page_text
        else:
            raise Exception(f"Azure OCR failed with status {result.status}")
