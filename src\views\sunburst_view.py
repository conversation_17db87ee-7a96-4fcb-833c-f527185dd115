import streamlit as st
import plotly.graph_objects as go
import pandas as pd
from typing import Dict, List, Any

class SunburstView:
    """
    View component for displaying expense distribution using a sunburst chart.
    Follows the MVC pattern as a View component.
    """

    def __init__(self, processed_data: List[Dict[str, Any]], sub_account_colors: Dict[str, str]):
        """
        Initialize with processed invoice data and color mappings.

        Args:
            processed_data: List of processed invoice data dictionaries
            sub_account_colors: Mapping of sub-account names to hex color codes
        """
        self.processed_data = processed_data
        self.sub_account_colors = sub_account_colors

    def render(self):
        """Renders the sunburst visualization in the Streamlit UI"""
        if not self.processed_data:
            st.info("No processed data available. Process invoices to view expense distribution.")
            return

        # Prepare data for sunburst chart
        chart_data = self._prepare_chart_data()

        positive_chart_data = chart_data['positive_chart']
        negative_chart_data = chart_data['negative_chart']

        if not positive_chart_data['labels'] and not negative_chart_data['labels']:
            st.warning("No line items found with sub-account information.")
            return

        # Create the sunburst chart for positive values
        if positive_chart_data['labels']:
            fig_positive = go.Figure(go.Sunburst(
                ids=positive_chart_data['ids'],
                labels=positive_chart_data['labels'],
                parents=positive_chart_data['parents'],
                values=positive_chart_data['values'],
                branchvalues='total',
                marker=dict(
                    colors=positive_chart_data['colors']
                ),
                textinfo='label+value',
                hovertemplate='<b>%{label}</b><br>Amount: $%{value:.2f}<br><extra></extra>'
            ))

            fig_positive.update_layout(
                title='Expenses',
                width=800,
                height=800,
                margin=dict(t=30, l=0, r=0, b=0)
            )

            # Display the chart
            st.plotly_chart(fig_positive, use_container_width=True)

            # Show supporting data
            with st.expander("View Expenses Data Table"):
                st.dataframe(positive_chart_data['dataframe'])

        # Create the sunburst chart for negative values
        if negative_chart_data['labels']:
            fig_negative = go.Figure(go.Sunburst(
                ids=negative_chart_data['ids'],
                labels=negative_chart_data['labels'],
                parents=negative_chart_data['parents'],
                values=negative_chart_data['values'],
                branchvalues='total',
                marker=dict(
                    colors=negative_chart_data['colors']
                ),
                textinfo='label+value',
                hovertemplate='<b>%{label}</b><br>Amount: $%{value:.2f}<br><extra></extra>'
            ))

            fig_negative.update_layout(
                title='Vendor Credits',
                width=800,
                height=800,
                margin=dict(t=30, l=0, r=0, b=0)
            )

            # Display the chart
            st.plotly_chart(fig_negative, use_container_width=True)

            # Show supporting data
            with st.expander("View Credits Data Table"):
                st.dataframe(negative_chart_data['dataframe'])

    def _prepare_chart_data(self):
        """Prepares data for the sunburst chart, separating negative line items."""
        # Extract line items with sub-account information
        all_line_items = []
        negative_line_items = []
        for data in self.processed_data:
            if 'parsed_data' in data and 'line_items' in data['parsed_data']:
                for item in data['parsed_data']['line_items']:
                    if 'sub_account' in item and 'total_price' in item:
                        amount = float(item['total_price'])
                        if amount < 0:
                            negative_line_items.append(item)
                        else:
                            all_line_items.append(item)

        # Handle case where no valid line items exist
        if not all_line_items and not negative_line_items:
            return {
                'positive_chart': {'ids': [], 'labels': [], 'parents': [], 'values': [], 'colors': [], 'dataframe': pd.DataFrame()},
                'negative_chart': {'ids': [], 'labels': [], 'parents': [], 'values': [], 'colors': [], 'dataframe': pd.DataFrame()}
            }

        def create_chart_data(line_items, is_negative=False):
            """Helper function to create chart data."""
            sub_account_totals = {}
            for item in line_items:
                sub_account = item.get('sub_account', 'Uncategorized')
                amount = abs(float(item.get('total_price', 0))) if is_negative else float(item.get('total_price', 0))

                if sub_account in sub_account_totals:
                    sub_account_totals[sub_account] += amount
                else:
                    sub_account_totals[sub_account] = amount

            # Create arrays for sunburst chart
            ids = ['root']
            labels = ['All Expenses (Negative)' if is_negative else 'All Expenses']
            parents = ['']
            values = [sum(sub_account_totals.values())]
            colors = ['#FFFFFF']  # Root node color

            # Add sub-account nodes
            for sub_account, total in sub_account_totals.items():
                ids.append(f'subacct_{sub_account}')
                labels.append(sub_account)
                parents.append('root')
                values.append(total)
                colors.append(self.sub_account_colors.get(sub_account, '#CCCCCC'))

            # Add individual items
            for i, item in enumerate(line_items):
                sub_account = item.get('sub_account', 'Uncategorized')
                description = item.get('description', f'Item {i + 1}')
                amount = abs(float(item.get('total_price', 0))) if is_negative else float(item.get('total_price', 0))

                # Truncate long descriptions
                short_desc = description[:40] + '...' if len(description) > 40 else description

                ids.append(f'item_{i}')
                labels.append(short_desc)
                parents.append(f'subacct_{sub_account}')
                values.append(amount)
                colors.append(self.sub_account_colors.get(sub_account, '#CCCCCC'))

            # Create dataframe for table view
            df_data = {
                'Description': [labels[i] for i in range(len(ids))],
                'Category': [parents[i] for i in range(len(ids))],
                'Amount': [values[i] for i in range(len(ids))]
            }
            df = pd.DataFrame(df_data)

            return {
                'ids': ids,
                'labels': labels,
                'parents': parents,
                'values': values,
                'colors': colors,
                'dataframe': df
            }

        # Create chart data for positive and negative line items
        positive_chart_data = create_chart_data(all_line_items)
        negative_chart_data = create_chart_data(negative_line_items, is_negative=True)

        return {
            'positive_chart': positive_chart_data,
            'negative_chart': negative_chart_data
        }