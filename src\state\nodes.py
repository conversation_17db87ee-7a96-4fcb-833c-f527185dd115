import os
import shutil
from src.state.state import ProcessState
from src.models.google_vision_ocr import GoogleVisionOCR
from src.models.azure_ocr import AzureOCR
import logging
from src.models.llm_factory import LLMFactory
from src.models.invoice_processor import InvoiceProcessor
from src.models.classifier_factory import ClassifierFactory
# Add these new functions to src/state/nodes.py
from src.models.chart_of_accounts import ChartOfAccounts
from src.models.determine_intent import IntentParser
from src.models.determine_frequency import FrequencyParser, InvoiceDateExtractor
from src.models.determine_project_code import CodesParser
from src.models.s3bucket import S3BucketRepository, FileLike
from src.models.invoice_detector_factory import InvoiceDetectorFactory
import json
import traceback

def detect_if_invoice_node(state: dict) -> dict:
    """
    Detects if the document is an invoice based on its text content.
    """
    detector_type = state.get("invoice_detector", "llm")
    print(f"[detect_if_invoice] Using detector type: {detector_type}")

    if detector_type == "llm":
        llm = LLMFactory().create_llm("openai", "gpt-4o")
        detector = InvoiceDetectorFactory.create_detector("llm", llm=llm)
    else:
        detector = InvoiceDetectorFactory.create_detector(detector_type)

    try:
        google_ocr_text = state.get("ocr_results", {}).get("google", "")
        if not google_ocr_text:
            print("[detect_if_invoice] No Google OCR text available for detection.")
            state["is_invoice"] = False
            return state

        is_invoice = detector.detect(google_ocr_text)
        state["is_invoice"] = is_invoice
        print(f"[detect_if_invoice] Document is an invoice: {is_invoice}")
        return state
    except Exception as e:
        print(f"[detect_if_invoice] Error detecting invoice: {e}")
        state["is_invoice"] = False
        return state



def write_json_file_node(state: dict) -> dict:
    """
    Uploads the enriched JSON data to an S3 bucket. The bucket name is derived from the user's email.
    """
    output_folder = state.get("output_folder", ".")
    filename = state.get("filename", "unknown")
    base_name, _ = os.path.splitext(filename)

    # Get the enriched data from the state
    if "parsed_data" in state and state["parsed_data"]:
        enriched_data = state["parsed_data"]
    else:
        print("[write_json_file_node] No enriched parsed_data found in state. Using empty object.")
        enriched_data = {}

    # Convert the enriched data to JSON
    try:
        json_content = json.dumps(enriched_data, indent=2).encode("utf-8")
    except Exception as e:
        print(f"[write_json_file_node] Error converting enriched data to JSON: {e}")
        return state

    # Get the bucket name from the user's email
    bucket_name = "pikernel-debug-results" # Placeholder for bucket name
    if not bucket_name:
        print("[write_json_file_node] User email not found. Cannot determine bucket name.")
        return state

    # Create the S3 repository and FileLike object
    try:
        s3_repo = S3BucketRepository()
        file_key = f"{base_name}_enriched.json"
        file_like = FileLike(filename=file_key, file_content=json_content)

        # Upload the file to the S3 bucket
        s3_repo.create(bucket=bucket_name, filename_key=file_key, file=file_like)
        print(f"[write_json_file_node] Successfully uploaded enriched JSON to S3 bucket '{bucket_name}' with key '{file_key}'")

        # Update the state with the S3 file path
        state["enriched_json_s3_path"] = f"s3://{bucket_name}/{file_key}"
    except Exception as e:
        print(f"[write_json_file_node] Error uploading JSON file to S3: {e}")

    return state



def intent_node(state: dict) -> dict:
    """
    Enriches invoice line items with sub-account information using an LLM.

    Analyzes each line item description and assigns the appropriate sub-account
    based on the organization's Chart of Accounts.
    """
    print("\n[intent_node] STARTING INTENT DETERMINATION")
    print(f"[intent_node] State keys: {list(state.keys())}")

    parsed_data = state.get("parsed_data", {})
    print(f"[intent_node] parsed_data type: {type(parsed_data)}")

    if isinstance(parsed_data, str):
        try:
            import json
            parsed_data = json.loads(parsed_data)
            print("[intent_node] Converted string parsed_data to dictionary")
        except Exception as e:
            print(f"[intent_node] Failed to parse JSON string: {e}")
            return state

    if not isinstance(parsed_data, dict) or "line_items" not in parsed_data:
        print(
            f"[intent_node] Invalid parsed_data structure. Keys: {list(parsed_data.keys()) if isinstance(parsed_data, dict) else 'not a dict'}")
        return state

    line_items = parsed_data.get("line_items", [])
    print(f"[intent_node] Number of line items: {len(line_items)}")

    # Ensure we have a chart of accounts
    if "chart_of_accounts" not in state or not state["chart_of_accounts"]:
        print("[intent_node] No chart_of_accounts in state, creating default")
        try:
            from src.models.chart_of_accounts import ChartOfAccounts
            chart_of_accounts = ChartOfAccounts.create("Excel")
            state["chart_of_accounts"] = chart_of_accounts
        except Exception as e:
            print(f"[intent_node] Error creating Chart of Accounts: {e}")
            return state
    else:
        chart_of_accounts = state["chart_of_accounts"]

    # Create the intent parser with the provided LLM model and memos
    provider = state.get("llm_provider", "openai")
    model_name = state.get("llm_model", "gpt-4o")
    memos = state.get("intent_memos", [])

    try:
        from src.models.llm_factory import LLMFactory
        import traceback

        llm_factory = LLMFactory()
        print(f"[intent_node] Creating LLM with {provider}/{model_name}")

        llm = llm_factory.create_llm(provider, model_name)
        print(f"[intent_node] Successfully created LLM: {type(llm).__name__}")

        from src.models.determine_intent import IntentParser
        intent_parser = IntentParser(llm, chart_of_accounts, memos)
        print(f"[intent_node] Created intent parser")

        # Process each line item
        for i, line_item in enumerate(line_items):
            description = line_item.get("description", "")
            if description:
                try:
                    print(f"[intent_node] Processing item {i + 1}: '{description[:50]}...'")
                    fully_qualified_account = intent_parser.determine_intent(description)
                    account_details = chart_of_accounts.get_account_mapping()[fully_qualified_account]
                    sub_account = account_details['AccountSubType']
                    print(f"[intent_node] Determined sub-account: {sub_account}")
                    line_item["sub_account"] = sub_account
                    # Optionally add more account details to the line item
                    line_item["account_id"] = account_details['Id']
                    line_item["account_type"] = account_details['AccountType']
                    line_item["account_name"] = account_details['Name']
                    line_item["currency"] = account_details['CurrencyRef.value']
                except Exception as e:
                    print(f"[intent_node] Error determining sub-account for item {i + 1}: {e}")
                    print(traceback.format_exc())
                    line_item["sub_account"] = "Other Miscellaneous Service Cost"  # Default fallback

        # Update state with enriched data
        state["parsed_data"] = parsed_data
        return state

    except Exception as e:
        import traceback
        print(f"[intent_node] Error in intent determination: {str(e)}")
        print(traceback.format_exc())
        return state

def project_codes_node(state: dict) -> dict:
    """
    Enriches invoice line items with project codes using an LLM.

    Analyzes each line item description and assigns the appropriate project codes
    based on the organization's accounting instructions.
    """
    print("\n[project_codes_node] STARTING PROJECT CODE DETERMINATION")
    print(f"[project_codes_node] State keys: {list(state.keys())}")

    parsed_data = state.get("parsed_data", {})
    print(f"[project_codes_node] parsed_data type: {type(parsed_data)}")

    if isinstance(parsed_data, str):
        try:
            import json
            parsed_data = json.loads(parsed_data)
            print("[project_codes_node] Converted string parsed_data to dictionary")
        except Exception as e:
            print(f"[project_codes_node] Failed to parse JSON string: {e}")
            return state
    
    if not isinstance(parsed_data, dict) or "line_items" not in parsed_data:
        print(
            f"[project_codes_node] Invalid parsed_data structure. Keys: {list(parsed_data.keys()) if isinstance(parsed_data, dict) else 'not a dict'}")
        return state
    
    line_items = parsed_data.get("line_items", [])
    print(f"[project_codes_node] Number of line items: {len(line_items)}")

    # Ensure we have memos
    if "project_code_memos" not in state or not state["project_code_memos"]:
        print("[project_codes_node] No memos in state, creating default")
        state["project_code_memos"] = []
    else:
        memos = state["project_code_memos"]

    # Create the codes parser with the provided LLM model and memos
    provider = state.get("llm_provider", "openai")
    model_name = state.get("llm_model", "gpt-4o")

    try:
        from src.models.llm_factory import LLMFactory
        import traceback

        llm_factory = LLMFactory()
        print(f"[project_codes_node] Creating LLM with {provider}/{model_name}")

        llm = llm_factory.create_llm(provider, model_name)
        print(f"[project_codes_node] Successfully created LLM: {type(llm).__name__}")

        from src.models.determine_project_code import CodesParser
        codes_parser = CodesParser(llm, memos)
        print(f"[project_codes_node] Created codes parser")

        # Process each line item
        for i, line_item in enumerate(line_items):
            description = line_item.get("description", "")
            if description:
                try:
                    import json
                    print(f"[project_codes_node] Processing item {i + 1}: '{description[:50]}...'")
                    codes = codes_parser.determine_project_codes(description)
                    print(f"[project_codes_node] Codes parser output: {codes}")
                    codes_dict = json.loads(codes)
                    print(f"[project_codes_node] Determined project codes: {codes_dict}")
                    line_item["project_codes"] = codes_dict["project_codes"]
                    line_item["employee_codes"] = codes_dict["employee_codes"]
                    line_item["department_codes"] = codes_dict["department_codes"]
                except Exception as e:
                    print(f"[project_codes_node] Error determining project codes for item {i + 1}: {e}")
                    print(traceback.format_exc())
                    line_item["project_codes"] = []
                    line_item["employee_codes"] = []
                    line_item["department_codes"] = []

        # Update state with enriched data
        state["parsed_data"] = parsed_data
        return state
    except Exception as e:
        import traceback
        print(f"[project_codes_node] Error in project code determination: {str(e)}")
        print(traceback.format_exc())
        return state

def frequency_node(state: dict) -> dict:
    """
    Enriches invoice line items with frequency (periodicity) information using an LLM.

    Analyzes each line item description and assigns the appropriate frequency
    (ANNUAL, SEMI-ANNUAL, QUARTERLY, or MONTHLY).
    """
    print("\n[frequency_node] STARTING FREQUENCY DETERMINATION")
    print(f"[frequency_node] State keys: {list(state.keys())}")

    parsed_data = state.get("parsed_data", {})
    print(f"[frequency_node] parsed_data type: {type(parsed_data)}")

    if isinstance(parsed_data, str):
        try:
            import json
            parsed_data = json.loads(parsed_data)
            print("[frequency_node] Converted string parsed_data to dictionary")
        except Exception as e:
            print(f"[frequency_node] Failed to parse JSON string: {e}")
            return state

    if not isinstance(parsed_data, dict) or "line_items" not in parsed_data:
        print(
            f"[frequency_node] Invalid parsed_data structure. Keys: {list(parsed_data.keys()) if isinstance(parsed_data, dict) else 'not a dict'}")
        return state

    line_items = parsed_data.get("line_items", [])
    invoice_date = parsed_data.get("invoice_header", {}).get("invoice_date", "")
    print(f"[frequency_node] Number of line items: {len(line_items)}")

    # Create the frequency parser with the provided LLM model
    provider = state.get("llm_provider", "openai")
    model_name = state.get("llm_model", "gpt-4o")

    try:
        from src.models.llm_factory import LLMFactory
        import traceback

        llm_factory = LLMFactory()
        print(f"[frequency_node] Creating LLM with {provider}/{model_name}")

        llm = llm_factory.create_llm(provider, model_name)
        print(f"[frequency_node] Successfully created LLM: {type(llm).__name__}")

        from src.models.determine_frequency import FrequencyParser
        frequency_parser = FrequencyParser(llm)
        invoice_date_extractor = InvoiceDateExtractor(llm)
        print(f"[frequency_node] Created frequency parser")

        # Process each line item
        for i, line_item in enumerate(line_items):
            description = line_item.get("description", "")
            if description:
                try:
                    print(f"[frequency_node] Processing item {i + 1}: '{description[:50]}...'")
                    frequency = frequency_parser.determine_frequency(description, invoice_date)
                    if frequency not in ["MONTHLY"]:
                        start_date, end_date = invoice_date_extractor.get_dates(description) 
                        line_item['service_start'] = start_date[0]
                        line_item['service_end'] = end_date[0]
                        line_item['date_format'] = start_date[1]
                    print(f"[frequency_node] Determined frequency: {frequency}")
                    line_item["frequency"] = frequency
                except Exception as e:
                    print(f"[frequency_node] Error determining frequency for item {i + 1}: {e}")
                    print(traceback.format_exc())
                    line_item["frequency"] = "MONTHLY"  # Default fallback

        # Update state with enriched data
        state["parsed_data"] = parsed_data
        return state

    except Exception as e:
        import traceback
        print(f"[frequency_node] Error in frequency determination: {str(e)}")
        print(traceback.format_exc())
        return state


def invoice_type_classifier_node(state: dict) -> dict:
    """Classifies the invoice based on Google OCR text"""
    google_ocr_text = state.get("ocr_results", {}).get("google", "")

    if not google_ocr_text:
        print("[invoice_type_classifier_node] No Google OCR text available for classification.")
        state["invoice_type"] = "UNKNOWN"
        return state

    # Create classifier using factory
    classifier = ClassifierFactory.create_classifier("word_count", threshold=1700)

    # Classify the invoice
    invoice_type = classifier.classify(google_ocr_text)
    state["invoice_type"] = invoice_type
    print(f"[invoice_type_classifier_node] Invoice classified as: {invoice_type}")

    return state


def compare_ocr_lengths_node(state: dict) -> dict:
    """Compares Google and Azure OCR results and selects the longer text"""
    google_text = state.get("ocr_results", {}).get("google", "")
    azure_text = state.get("ocr_results", {}).get("azure", "")

    google_length = len(google_text)
    azure_length = len(azure_text)

    print(f"[compare_ocr_lengths_node] Google OCR text length: {google_length}")
    print(f"[compare_ocr_lengths_node] Azure OCR text length: {azure_length}")

    if azure_length > google_length:
        state["selected_ocr_provider"] = "azure"
        state["selected_ocr_text"] = azure_text
        print("[compare_ocr_lengths_node] Selected Azure OCR (longer text)")
    else:
        state["selected_ocr_provider"] = "google"
        state["selected_ocr_text"] = google_text
        print("[compare_ocr_lengths_node] Selected Google OCR (longer text)")

    return state


def route_by_invoice_type(state: dict) -> str:
    """Determine next node based on invoice type"""
    invoice_type = state.get("invoice_type", "UNKNOWN")

    if invoice_type == "COMPLEX":
        print(f"[route_by_invoice_type] COMPLEX invoice: routing to Azure OCR")
        return "azure_ocr"
    else:
        print(f"[route_by_invoice_type] REGULAR invoice: preparing Google OCR text for LLM")
        # For REGULAR invoices, set Google text as selected text directly
        state["selected_ocr_provider"] = "google"
        state["selected_ocr_text"] = state.get("ocr_results", {}).get("google", "")
        return "write_text"


def copy_file_node(state: dict) -> dict:
    """
    Copies the file to the output folder and uploads it to an S3 bucket.
    """
    input_folder = state["input_folder"]
    output_folder = state["output_folder"]
    filename = state["filename"]
    input_path = os.path.join(input_folder, filename)
    output_path = os.path.join(output_folder, filename)

    # Copy the file locally to the output folder
    try:
        shutil.copy2(input_path, output_path)
        print(f"[copy_file_node] Copied {filename} to {output_folder}")
        state["invoices"][filename] = "Copied successfully"
    except Exception as e:
        print(f"[copy_file_node] Error copying {filename} to {output_folder}: {e}")
        state["invoices"][filename] = f"Error: {e}"

    # Upload the file to the S3 bucket
    bucket_name = "pikernel-debug-results"  # Placeholder for bucket name
    if not bucket_name:
        print("[copy_file_node] User email not found. Cannot determine bucket name.")
        return state

    try:
        # Read the file content
        with open(input_path, "rb") as file_obj:
            file_content = file_obj.read()

        # Create the S3 repository and upload the file
        s3_repo = S3BucketRepository()
        file_key = filename  # Use the filename as the key in the bucket
        file = FileLike(filename=file_key, file_content=file_content)
        s3_repo.create(bucket=bucket_name, filename_key=file_key, file=file)

        print(f"[copy_file_node] Successfully uploaded {filename} to S3 bucket '{bucket_name}' with key '{file_key}'")

        # Update the state with the S3 file path
        state["invoices"][filename] = f"s3://{bucket_name}/{file_key}"
    except Exception as e:
        print(f"[copy_file_node] Error uploading {filename} to S3: {e}")
        state["invoices"][filename] = f"Error: {e}"

    return state

def google_vision_ocr_node(state: dict) -> dict:
    output_folder = state["output_folder"]
    filename = state["filename"]
    file_path = os.path.join(output_folder, filename)
    ocr_engine = GoogleVisionOCR()
    try:
        print(f"[google_vision_ocr_node] Extracting text from {file_path}")
        ocr_result = ocr_engine.extract_text(file_path)  # Returns an OCRResult
        extracted_text = ocr_result.text
        print(f"[google_vision_ocr_node] Extracted text (first 100 chars): {extracted_text[:100]}")
        state["ocr_results"]["google"] = extracted_text
        state["ocr_confidences"]["google"] = ocr_result.confidence_stats
    except Exception as e:
        print(f"[google_vision_ocr_node] Error extracting text: {e}")
        state["ocr_results"]["google"] = ""
        state["ocr_confidences"]["google"] = {}
    return state

# New Azure OCR node:
def azure_ocr_node(state: dict) -> dict:
    output_folder = state["output_folder"]
    filename = state["filename"]
    file_path = os.path.join(output_folder, filename)
    try:
        print(f"[azure_ocr_node] Extracting text from {file_path} using Azure OCR")
        azure_engine = AzureOCR()  # Reads endpoint and key from environment variables
        ocr_result = azure_engine.extract_text(file_path)  # Returns an OCRResult
        extracted_text = ocr_result.text
        print(f"[azure_ocr_node] Extracted text (first 100 chars): {extracted_text[:100]}")
        state["ocr_results"]["azure"] = extracted_text
        state["ocr_confidences"]["azure"] = ocr_result.confidence_stats
    except Exception as e:
        print(f"[azure_ocr_node] Error extracting text: {e}")
        state["ocr_results"]["azure"] = ""
        state["ocr_confidences"]["azure"] = {}
    return state

def ocr_selection_node(state: dict) -> dict:
    ocr_results = state.get("ocr_results", {})
    ocr_confidences = state.get("ocr_confidences", {})
    if not ocr_results or not ocr_confidences:
        print("[ocr_selection_node] No OCR results available.")
        state["selected_ocr_provider"] = "none"
        state["selected_ocr_text"] = ""
        return state

    best_provider = None
    best_confidence = -1.0
    for provider, metrics in ocr_confidences.items():
        avg_conf = metrics.get("mean", 0.0)
        text_length = len(ocr_results.get(provider, ""))
        score = avg_conf * 0.7 + (text_length / 1000) * 0.3  # Composite score
        print(f"[ocr_selection_node] Provider '{provider}': avg_conf={avg_conf:.3f}, text_length={text_length}, score={score:.3f}")
        if score > best_confidence:
            best_confidence = score
            best_provider = provider

    if best_provider is None:
        best_provider = "unknown"

    state["selected_ocr_provider"] = best_provider
    state["selected_ocr_text"] = ocr_results.get(best_provider, "")
    print(f"[ocr_selection_node] Selected provider: {best_provider} with score {best_confidence:.3f}")
    return state

def write_text_file_node(state: dict) -> dict:
    """
    Uploads the selected OCR text to an S3 bucket. The bucket name is derived from the user's email.
    """
    output_folder = state.get("output_folder", ".")
    filename = state.get("filename", "unknown")
    provider = state.get("selected_ocr_provider", "unknown")
    base_name, _ = os.path.splitext(filename)

    # Get the selected OCR text from the state
    ocr_text = state.get("selected_ocr_text", "")
    if not ocr_text:
        print("[write_text_file_node] No OCR text found in state.")
        return state

    # Convert the OCR text to bytes
    try:
        text_content = ocr_text.encode("utf-8")
    except Exception as e:
        print(f"[write_text_file_node] Error encoding OCR text: {e}")
        return state

    # Get the bucket name from the user's email
    bucket_name = "pikernel-debug-results"  # Placeholder for bucket name
    if not bucket_name:
        print("[write_text_file_node] User email not found. Cannot determine bucket name.")
        return state

    # Create the S3 repository and FileLike object
    try:
        s3_repo = S3BucketRepository()
        file_key = f"{base_name}_{provider}.txt"
        file_like = FileLike(filename=file_key, file_content=text_content)

        # Upload the file to the S3 bucket
        s3_repo.create(bucket=bucket_name, filename_key=file_key, file=file_like)
        print(f"[write_text_file_node] Successfully uploaded OCR text to S3 bucket '{bucket_name}' with key '{file_key}'")

        # Update the state with the S3 file path
        state["ocr_text_s3_path"] = f"s3://{bucket_name}/{file_key}"
    except Exception as e:
        print(f"[write_text_file_node] Error uploading OCR text to S3: {e}")

    return state

def azure_ocr_node(state: dict) -> dict:
    output_folder = state["output_folder"]
    filename = state["filename"]
    file_path = os.path.join(output_folder, filename)
    print(f"[azure_ocr_node] Extracting text from {file_path} using Azure OCR")
    try:
        ocr_engine = AzureOCR()  # Credentials should be loaded via dotenv (see below)
        ocr_result = ocr_engine.extract_text(file_path)  # Returns an OCRResult object
        extracted_text = ocr_result.text
        print(f"[azure_ocr_node] Extracted text (first 100 chars): {extracted_text[:100]}")
        state["ocr_results"]["azure"] = extracted_text
        state["ocr_confidences"]["azure"] = ocr_result.confidence_stats
    except Exception as e:
        print(f"[azure_ocr_node] Error extracting text: {e}")
        state["ocr_results"]["azure"] = ""
        state["ocr_confidences"]["azure"] = {}
    return state


def llm_parser_node(state: dict) -> dict:
    """
    Uses an LLM to parse the selected OCR text into a JSON string,
    then loads that JSON into state["parsed_data"] so that subsequent enrichment
    nodes (intent and frequency) can update it.
    """
    selected_text = state.get("selected_ocr_text", "")
    if not selected_text.strip():
        print("[llm_parser_node] No OCR text available for LLM parsing.")
        return state

    llm_provider = state.get("llm_provider", "openai")
    llm_model = state.get("llm_model", "gpt-4o")
    print(f"[llm_parser_node] Using LLM provider: {llm_provider} and model: {llm_model}")

    factory = LLMFactory()
    try:
        print(f"[llm_parser_node] Creating LLM with model: {llm_model}")
        llm = factory.create_llm(llm_provider, model_name=llm_model)
        print(f"[llm_parser_node] LLM instance created: {llm}")
    except Exception as e:
        print(f"[llm_parser_node] Error creating LLM: {e}")
        state.setdefault("parsed_jsons", {})[f"{state.get('selected_ocr_provider', 'unknown')}_error"] = f"LLM creation error: {str(e)}"
        return state

    invoice_processor = InvoiceProcessor(llm)
    try:
        print(f"[llm_parser_node] Processing invoice text (length: {len(selected_text)} chars)")
        parsed_json = invoice_processor.process_invoice_text(selected_text)
        # Update state["parsed_data"] with the LLM output
        try:
            state["parsed_data"] = json.loads(parsed_json)
        except Exception as e:
            print(f"[llm_parser_node] Error converting parsed JSON string to dict: {e}")
            state["parsed_data"] = {}
        print(f"[llm_parser_node] Updated state['parsed_data']: {state['parsed_data']}")
    except Exception as e:
        print(f"[llm_parser_node] Error processing invoice text: {e}")
        print(traceback.format_exc())
        state.setdefault("parsed_jsons", {})[f"{state.get('selected_ocr_provider', 'unknown')}_error"] = str(e)
    return state

def amazon_textract_node(state: dict) -> dict:
    """
    Process Amazon Textract OCR results to extract tables and convert them to CSV format.
    This node is used when the LLM parser fails to extract line items.
    """
    print("\n[amazon_textract_node] STARTING AMAZON TEXTRACT TABLE EXTRACTION")
    
    # Get the file path from state
    output_folder = state.get("output_folder", "")
    filename = state.get("filename", "")
    file_path = os.path.join(output_folder, filename)
    
    try:
        # Initialize the Amazon Textract OCR engine
        from src.models.aws_textract_ocr import AmazonTextractOCR
        textract_engine = AmazonTextractOCR()
        
        print(f"[amazon_textract_node] Extracting text from {file_path}")
        ocr_result = textract_engine.extract_text(file_path)
        
        # Extract tables from the OCR result
        tables = ocr_result.extract_tables()
        print(f"[amazon_textract_node] Extracted {len(tables)} tables from the document")
        
        if not tables:
            print("[amazon_textract_node] No tables found in the document")
            return state
        
        # Convert all tables to CSV format
        import io
        csv_output = io.StringIO()
        
        for i, table_df in enumerate(tables):
            csv_output.write(f"\n\n--- TABLE {i+1} ---\n\n")
            csv_output.write(table_df.to_csv(index=False))
        
        # Store the CSV output in selected_ocr_text
        state["selected_ocr_text"] += csv_output.getvalue()
        print(f"[amazon_textract_node] Converted {len(tables)} tables to CSV format")
        
        # Store the original OCR result in state
        state["ocr_results"]["amazon_textract"] = ocr_result.text
        
        # Also store the raw tables for potential further processing
        state["textract_tables"] = tables
        
    except Exception as e:
        import traceback
        print(f"[amazon_textract_node] Error processing Amazon Textract results: {str(e)}")
        print(traceback.format_exc())
    
    # update recursion counter
    state["textract_recursion_counter"] += 1
    if state["textract_recursion_counter"] > 1:
        print(f"[amazon_textract_node] Recursion limit reached (invoice controller graph looped over {state['textract_recursion_counter']} times), stopping further Textract processing")
        return state
    
    return state

def post_bill_node(state: dict) -> dict:
    """
    Posts the bill to the selected accounting package.
    """
    print("\n[post_bill_node] STARTING BILL POSTING")
    print(f"[post_bill_node] Selected accounting package: {state.get('accounting_package', 'quickbooks')}")
    print(f"[post_bill_node] Parsed data: {state.get('parsed_data', {})}")
    
    try:
        from src.models.accounting_package_factory import AccountingPackageFactory
        factory = AccountingPackageFactory()
        package = factory.create_accountingpackage(state.get('accounting_package', 'quickbooks'))
        
        if package:
            print(f"[post_bill_node] Posting bill to {state.get('accounting_package', 'quickbooks')}")
            package.post_invoice(json.dumps(state['parsed_data']))
        else:
            print(f"[post_bill_node] No accounting package connected, skipping post_bill_node")
        state["bill_posted"] = True
    except Exception as e:
        import traceback
        print(f"[post_bill_node] Error posting bill: {str(e)}")
        print(traceback.format_exc())
    
    return state



def check_lineitems(state: dict) -> bool:
    """
    Routes based on parsed_data content quality.
    Returns True if line items are empty (needs Textract), False otherwise.
    """
    parsed_data = state.get("parsed_data", {})
    line_items = parsed_data.get("line_items", [])
    
    if not line_items:
        print("[check_lineitems] No line items found, routing to amazon_textract")
        return True
    
    if len(line_items) == 0:
        print("[check_lineitems] No line items found, routing to amazon_textract")
        return True
    
    print(f"[check_lineitems] Found {len(line_items)} line items, routing to intent")
    return False
