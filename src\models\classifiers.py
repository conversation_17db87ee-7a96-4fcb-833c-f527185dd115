# src/models/classifiers.py
from abc import ABC, abstractmethod
from typing import Dict


class InvoiceClassifier(ABC):
    """Interface for invoice classification strategies"""

    @abstractmethod
    def classify(self, text: str) -> str:
        """Classify an invoice based on its text content"""
        pass


class WordCountClassifier(InvoiceClassifier):
    """Classifies invoices based on word count"""

    def __init__(self, threshold: int = 1800):
        self.threshold = threshold

    def classify(self, text: str) -> str:
        word_count = len(text.split())
        print(f"[WordCountClassifier] Invoice word count: {word_count}")

        if word_count > self.threshold:
            return "COMPLEX"
        else:
            return "REGULAR"