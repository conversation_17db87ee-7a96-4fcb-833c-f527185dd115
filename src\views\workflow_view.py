import streamlit as st


class WorkflowView:
    """
    View component for displaying the workflow graph visualization.
    Follows the MVC pattern as a View component.
    """

    def __init__(self, graph_image: bytes = None):
        """
        Initialize with the graph image data.

        Args:
            graph_image: PNG bytes of the workflow graph
        """
        self.graph_image = graph_image

    def render(self):
        """Renders the workflow graph in the Streamlit UI"""
        if self.graph_image:
            st.image(self.graph_image)
        else:
            st.info(
                "Workflow graph not available. Process invoices to generate the graph visualization."
            )

            # Show a placeholder/example of what the graph would look like
            st.markdown("""
            The workflow graph visualizes the invoice processing pipeline, including:
            1. OCR text extraction
            2. Invoice classification
            3. Data parsing
            4. Sub-account and frequency determination

            Once you process an invoice, you'll see the actual graph here.
            """)