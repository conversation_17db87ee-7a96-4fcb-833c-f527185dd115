# src/models/accounting_memo.py
import datetime
import pickle
import os

class AccountingMemo:
    def __init__(self, text: str):
        self.text = text
        self.created_date = datetime.datetime.now()
        self.modified_date = datetime.datetime.now()

def load_memos(filepath: str) -> list[AccountingMemo]:
    try:
        with open(filepath, 'rb') as f:
            return pickle.load(f)
    except FileNotFoundError:
        # If the file or directory doesn't exist, create them
        directory = os.path.dirname(filepath)
        if not os.path.exists(directory):
            os.makedirs(directory)
        return []  # Return an empty list
    except EOFError:
        return []

def save_memos(filepath: str, memos: list[AccountingMemo]):
    try:
        with open(filepath, 'wb') as f:
            pickle.dump(memos, f)
    except Exception as e:
        print(f"Error saving memos: {e}")

class MemoRepository:
    def __init__(self, filepath: str):
        self.filepath = filepath
        self.memos : list[AccountingMemo] = load_memos(filepath)

    def read_all_memos(self) -> list[AccountingMemo]:
        return self.memos
    
    def read_memo(self, index: int) -> AccountingMemo:
        return self.memos[index]
    
    def create_memo(self, text: str) -> AccountingMemo:
        memo = AccountingMemo(text)
        self.memos.append(memo)
        save_memos(self.filepath, self.memos)
        return memo
    
    def update_memo(self, index: int, text: str) -> AccountingMemo:
        memo = self.memos[index]
        memo.text = text
        memo.modified_date = datetime.datetime.now()
        save_memos(self.filepath, self.memos)
        return memo
    
    def delete_memo(self, index: int) -> AccountingMemo:
        memo = self.memos.pop(index)
        save_memos(self.filepath, self.memos)
        return memo