import streamlit as st


def render_tabs():
    """
    Creates and returns the application tabs.

    Returns:
        tuple: Tab objects for different views
    """
    tabs = st.tabs([
        "Process Invoices",
        "OCR Results",
        "Workflow Graph",
        "Formatted Invoices",
 #       "Expense Sunburst",
        "Account Network",
        "Schedules",
        "Business Intelligence",
        "Firm-specific Instructions"
    ])

    return tabs
