import os
from src.state.nodes import BaseNode
from src.state.state import ProcessState
from src.models.google_vision_ocr import GoogleVisionOCR

class GoogleVisionOCRNode(BaseNode):
    def __init__(self, file_path: str):
        # file_path is the location of the invoice file to process.
        self.file_path = file_path
        self.ocr_engine = GoogleVisionOCR()

    def execute(self, state: ProcessState) -> ProcessState:
        try:
            print(f"[GoogleVisionOCRNode] Extracting text from {self.file_path}")
            text = self.ocr_engine.extract_text(self.file_path)
            print(f"[GoogleVisionOCRNode] Extracted text (first 100 chars): {text[:100]}")
            state.update_ocr_text(text)
        except Exception as e:
            print(f"[GoogleVisionOCRNode] Error extracting text: {e}")
            state.update_ocr_text("")
        return state
