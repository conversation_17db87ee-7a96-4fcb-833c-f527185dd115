#!/usr/bin/env python3
# python -m src.cli.main --input_folder input_invoices --output_folder json_outputs --llm openai
import os
from dotenv import load_dotenv
import argparse
from pathlib import Path

# Load environment variables at the beginning.
load_dotenv()

from src.controllers.invoice_controller_graph import InvoiceController

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_folder", default="input_invoices")
    parser.add_argument("--output_folder", default="json_outputs")
    parser.add_argument("--llm", default="openai")
    # New argument for model name:
    parser.add_argument("--model", default="gpt-4o")
    
    # Get supported file types from environment
    supported_types_str = os.getenv("SUPPORTED_FILE_TYPES", "pdf,png,jpg,jpeg,tiff,tif")
    supported_extensions = [f".{ext.strip()}" for ext in supported_types_str.lower().split(",")]
    
    args = parser.parse_args()
    
    # Filter files in input folder by supported extensions
    input_files = []
    for file in os.listdir(args.input_folder):
        file_path = os.path.join(args.input_folder, file)
        if os.path.isfile(file_path) and Path(file).suffix.lower() in supported_extensions:
            input_files.append(file_path)
    
    if not input_files:
        print(f"No supported files found in {args.input_folder}. Supported types: {supported_types_str}")
        return
        
    controller = InvoiceController(args.input_folder, args.output_folder)
    # We pass both the LLM provider and (inside ProcessState) the llm_model will be set.
    # Our InvoiceController will construct ProcessState accordingly.
    # (Make sure your InvoiceController is updated to pass args.model.)
    controller.run_with_llm(args.llm, args.model)

if __name__ == "__main__":
    main()
