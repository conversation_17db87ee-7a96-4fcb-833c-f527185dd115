#!/usr/bin/env python3
import sys
import subprocess
import os


def run_test_and_capture_log(log_file):
    """
    Runs the test_invoice_processing module and writes output to log_file.
    """
    # Use the same Python interpreter (sys.executable)
    cmd = [sys.executable, "-m", "src.cli.test_invoice_processing"]
    with open(log_file, "w", encoding="utf-8") as f:
        subprocess.run(cmd, stdout=f, stderr=subprocess.STDOUT)
    print(f"Test output written to {log_file}")
    return log_file


def run_grep(pattern, log_file):
    """
    Runs a grep command on the log_file using the provided pattern.
    Returns the grep output.
    """
    try:
        result = subprocess.check_output(["grep", "-E", pattern, log_file], text=True)
    except subprocess.CalledProcessError as e:
        # grep returns non-zero if no matches are found
        result = e.output
    return result


def main():
    # Define an absolute path for the logfile (adjust as needed)
    log_file = os.path.join(os.getcwd(), "log_output.txt")

    # Run the test and capture log output
    run_test_and_capture_log(log_file)

    # Define grep patterns that focus on our debugging areas
    grep_patterns = [
        r"STARTING (INTENT|FREQUENCY) DETERMINATION",
        r"Processing item [0-9]+",
        r"Determined sub-account:",
        r"Determined frequency:",
        r"Error",
        r"Exception",
        r"Parsed data keys:"
    ]

    print("\n--- GREP RESULTS ---")
    for pattern in grep_patterns:
        print(f"\n=== Pattern: {pattern} ===")
        output = run_grep(pattern, log_file)
        if output.strip():
            print(output)
        else:
            print("No matches found.")


if __name__ == "__main__":
    main()
