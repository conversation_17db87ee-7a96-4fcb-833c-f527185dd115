import os
import io
import time
from pathlib import Path
from pdf2image import convert_from_path
from src.models.ocr_result import OCRResult, TextBlock
from src.models.s3bucket import S3BucketRepository, FileLike
from typing import List
from json import loads

class AmazonTextractOCR:
    def __init__(self):
        print("[AmazonTextractOCR] Initializing AmazonTextractOCR...")
        print("[AmazonTextractOCR] Initializing S3 repository...")
        # initialize S3 repository; credentials are handled by src/models/s3bucket.py
        self.s3_repo = S3BucketRepository()
        print("[AmazonTextractOCR] Initialized AmazonTextractOCR.")
    def extract_text(self, file_path: str) -> OCRResult:
        path = Path(file_path)
        # check if file exists
        if not path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")

        # upload to S3
        with open(file_path, "rb") as f:
            content = f.read()
        file_like = FileLike(filename=path.name, file_content=content)
        self.s3_repo.create(bucket="pillar-textract", filename_key=path.name, file=file_like)

        # obtain the file name (without extension)
        file_name = os.path.splitext(os.path.basename(file_path))[0]

        # this is the filename we expect textract to return in S3 bucket
        textract_output_filename = f"{file_name}_analysis.json"

        start_time = time.time()
        while textract_output_filename not in [obj['Key'] for obj in self.s3_repo.list_objects(Bucket="pillar-textract-results")['Contents']]:
            if time.time() - start_time > 30:
                raise TimeoutError("Amazon Textract analysis failed or took too long to complete.")
            pass

        results = self.s3_repo.read(bucket="pillar-textract-results", filename_key=textract_output_filename)
        results_content = results.read()
        # decode if the content is bytes
        if type(results_content) == bytes:
            results_content = results_content.decode("utf-8")

        text_blocks = self._detect_text_blocks(results_content) # call the _detect_text_blocks method to obtain text blocks from the results content
        return OCRResult(results_content, text_blocks, provider="Amazon Textract") # return an OCRResult object with the text and text blocks

    def _detect_text_blocks(self, content: str) -> List[TextBlock]: # added return type hint to specify that the method returns a list of TextBlock objects
        analysis = loads(content)
        blocks = analysis["Blocks"]
        block_dictionary = {} # dictionary to store blocks by ID
        # filter blocks that are not LINE, WORD, TABLE, or CELL
        try: 
            blocks = [
                block for block in blocks
                if block["BlockType"] in ["LINE", "WORD", "TABLE", "CELL"]
            ]
        except KeyError as e:
            print(f"[AmazonTextractOCR] Error: {e}")
            print(f"[AmazonTextractOCR] Content: {content}")
            raise e
        for block in blocks:
            print(f"[AmazonTextractOCR] Analyzing block {block["Id"]}")
            # we're only concerned with LINE, WORD, TABLE, and CELL blocks
            block_type = block["BlockType"]
            # get the text 
            if block_type == "TABLE":
                text = None
            else:
                text = block.get("Text", "")
            provider_details = {}
            provider_details["Id"] = block["Id"]
            # if the block is a cell, add the row and column index
            if block_type == "CELL":
                provider_details["RowIndex"] = block["RowIndex"]
                provider_details["ColumnIndex"] = block["ColumnIndex"]
            block_dictionary[block["Id"]] = TextBlock(
                text=text,
                confidence=block["Confidence"],
                block_type=block_type,
                page=block["Page"],
                position=block["Geometry"]["BoundingBox"],
                provider_details=provider_details
            )
            print(f"[AmazonTextractOCR] Results: {block_dictionary[block["Id"]]}")

        # iterate over the blocks again and add children
        for block in blocks:
            block_id = block["Id"]
            list_of_children = []
            # obtain the children using the dictionary 
            #for relation in block["Relations"]:
            #    if relation["Type"] == "CHILD":
            #        for child_id in relation["Ids"]:
            #            list_of_children.append(child_id)
            if "Relationships" in block.keys():
                for relation in block["Relationships"]:
                    if relation["Type"] == "CHILD":
                        for child_id in relation["Ids"]:
                            list_of_children.append(child_id)
                block_dictionary[block_id].children = [block_dictionary[child_id] for child_id in list_of_children]

        return block_dictionary.values() # return the values of the dictionary as a list of TextBlock objects