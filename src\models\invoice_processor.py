import json
import re
from jsonschema import validate, ValidationError
from langchain_core.messages import SystemMessage, HumanMessage

import json
import re
from jsonschema import validate, ValidationError


class JsonOutputParser:
    def __init__(self, json_schema: dict):
        self.json_schema = json_schema

    def parse(self, text: str) -> str:
        try:
            cleaned_text = self._extract_json_from_text(text)
            print(f"[JsonOutputParser] Attempting to parse text (first 200 chars): {cleaned_text[:200]}...")
            parsed = json.loads(cleaned_text)

            # Fix: if 'line_items' is a dict with an 'items' key, extract the list.
            if "line_items" in parsed and isinstance(parsed["line_items"], dict) and "items" in parsed["line_items"]:
                print("[JsonOutputParser] Detected nested 'line_items'; extracting list from 'items' key.")
                parsed["line_items"] = parsed["line_items"]["items"]

            print(f"[JsonOutputParser] Successfully loaded as JSON")
            print(f"[JsonOutputParser] JSON keys: {list(parsed.keys())}")
            validate(instance=parsed, schema=self.json_schema)
            print(f"[JsonOutputParser] JSON validation successful")
            return json.dumps(parsed, indent=2)
        except json.JSONDecodeError as e:
            print(f"[JsonOutputParser] JSON decode error: {e}")
            print(f"[JsonOutputParser] Problem at character position: {e.pos}")
            snippet = text[max(0, e.pos - 20):min(len(text), e.pos + 20)]
            print(f"[JsonOutputParser] Text snippet around error: '{snippet}'")
            raise Exception(f"Failed to parse JSON: {str(e)}")
        except ValidationError as e:
            print(f"[JsonOutputParser] JSON validation error: {e}")
            print(f"[JsonOutputParser] Path to error: {list(e.path)}")
            print(f"[JsonOutputParser] Schema path: {list(e.schema_path)}")
            raise Exception(f"Failed to validate JSON: {str(e)}")

    def _extract_json_from_text(self, text: str) -> str:
        code_block_pattern = r"```(?:json)?\s*([\s\S]*?)\s*```"
        matches = re.findall(code_block_pattern, text)
        if matches:
            print(f"[JsonOutputParser] Extracted JSON from code block")
            return matches[0].strip()

        if '{' in text and '}' in text:
            start_idx = text.find('{')
            end_idx = text.rfind('}') + 1
            print(f"[JsonOutputParser] Extracted JSON using brace matching")
            return text[start_idx:end_idx].strip()

        return text.strip()


class InvoiceProcessor:
    def __init__(self, llm):
        self.llm = llm
        print(f"[InvoiceProcessor] Initialized with LLM: {type(self.llm).__name__}")

        self.json_schema = {
            "type": "object",
            "properties": {
                "invoice_header": {
                    "type": "object",
                    "properties": {
                        "invoice_number": {"type": ["string", "null"]},
                        "invoice_date": {"type": ["string", "null"]},
                        "due_date": {"type": ["string", "null"]},
                        "supplier": {
                            "type": ["object", "null"],
                            "properties": {
                                "name": {"type": ["string", "null"]},
                                "address": {
                                    "type": ["object", "null"],
                                    "properties": {
                                        "street": {"type": ["string", "null"]},
                                        "city": {"type": ["string", "null"]},
                                        "state": {"type": ["string", "null"]},
                                        "zip_code": {"type": ["string", "null"]},
                                        "country": {"type": ["string", "null"]}
                                    }
                                },
                                "contact": {
                                    "type": ["object", "null"],
                                    "properties": {
                                        "name": {"type": ["string", "null"]},
                                        "email": {"type": ["string", "null"]},
                                        "phone": {"type": ["string", "null"]}
                                    }
                                }
                            }
                        },
                        "supplier_type": {
                            "type": ["object", "null"],
                            "properties": {
                                "supplier_category": {"type": ["string", "null"]}
                            }
                        },
                        "customer": {
                            "type": ["object", "null"],
                            "properties": {
                                "name": {"type": ["string", "null"]},
                                "address": {
                                    "type": ["object", "null"],
                                    "properties": {
                                        "street": {"type": ["string", "null"]},
                                        "city": {"type": ["string", "null"]},
                                        "state": {"type": ["string", "null"]},
                                        "zip_code": {"type": ["string", "null"]},
                                        "country": {"type": ["string", "null"]}
                                    }
                                },
                                "contact": {
                                    "type": ["object", "null"],
                                    "properties": {
                                        "name": {"type": ["string", "null"]},
                                        "email": {"type": ["string", "null"]},
                                        "phone": {"type": ["string", "null"]}
                                    }
                                }
                            }
                        },
                        "currency": {"type": ["string", "null"]},
                        "var_total": {"type": ["number", "null"]},
                        "sales_tax_total": {"type": ["number", "null"]},
                        "total_amount": {"type": ["number", "null"]}
                    }
                },
                "line_items": {
                    "type": ["array", "null"],
                    "items": {
                        "type": "object",
                        "properties": {
                            "date": {"type": ["string", "null"]},
                            "description": {"type": ["string", "null"]},
                            "quantity": {"type": ["number", "null"]},
                            "unit_price": {"type": ["number", "null"]},
                            "total_price": {"type": ["number", "null"]},
                            "sub_account": {"type": ["string", "null"]},
                            "account_id": {"type": ["number", "null"]},
                            "frequency": {
                                "type": ["string", "null"],
                                "enum": ["SINGLE", "ANNUAL", "SEMI-ANNUAL", "QUARTERLY", "MONTHLY", None]
                            },
                            "department_codes": {
                                "type": ["array", "null"],
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "department_code": {"type": ["string", "null"]}
                                    }
                                }
                            },
                            "employee_codes": {
                                "type": ["array", "null"],
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "employee_code": {"type": ["string", "null"]}
                                    }
                                }
                            },
                            "project_codes": {
                                "type": ["array", "null"],
                                "items": {
                                    "type": "object",
                                    "properties": {
                                        "project_code": {"type": ["string", "null"]}
                                    }
                                }
                            }
                        }
                    }
                },
                "supplier_bank_details": {
                    "type": ["array", "null"],
                    "items": {
                        "type": "object",
                        "properties": {
                            "bank_account_name": {"type": ["string", "null"]},
                            "bank_account_number": {"type": ["string", "null"]},
                            "bank_iban": {"type": ["string", "null"]},
                            "bank_swift": {"type": ["string", "null"]},
                            "bank_address": {"type": ["string", "null"]}
                        }
                    }
                },
                "supplier_corp_details": {
                    "type": ["array", "null"],
                    "items": {
                        "type": "object",
                        "properties": {
                            "supplier_vat_number": {"type": ["string", "null"]},
                            "supplier_sales_tax_number": {"type": ["string", "null"]},
                            "supplier_company_registration_number": {"type": ["string", "null"]},
                            "supplier_registration_jurisdiction": {"type": ["string", "null"]},
                            "supplier_registered_address": {"type": ["string", "null"]}
                        }
                    }
                },
                "accounting_app_posting": {
                    "type": ["array", "null"],
                    "items": {
                        "type": "object",
                        "properties": {
                            "app_bill_id": {"type": ["string", "null"]},
                            "app_audit_id": {"type": ["string", "null"]},
                            "app_posting_datetime_stamp": {"type": ["string", "null"]},
                            "invoice_filename": {"type": ["string", "null"]},
                            "posting_username": {"type": ["string", "null"]},
                            "username": {"type": ["string", "null"]}
                        }
                    }
                },
                "graphdb_posting": {
                    "type": ["array", "null"],
                    "items": {
                        "type": "object",
                        "properties": {
                            "graphdb_id": {"type": ["string", "null"]},
                            "graphdb_datetime_stamp": {"type": ["string", "null"]},
                            "invoice_filename": {"type": ["string", "null"]}
                        }
                    }
                }
            }
        }

        self.schema_str = json.dumps(self.json_schema, indent=2)
        self.output_parser = JsonOutputParser(self.json_schema)

        system_message = (
            "You are an expert at extracting information from invoices. Extract the following details "
            "from the given invoice text and format the output according to the provided JSON schema. "
            "It is CRITICAL that you ONLY use the exact keys and structure specified in the schema. Do not add "
            "any additional fields or modify the structure in any way. If information is missing or not applicable, "
            "use null values or empty strings/arrays as appropriate.\n\n"
            "IMPORTANT: For every object in the schema, include ALL child properties, even if their values are null. "
            "For example, if an 'address' object is missing, it should still be included as: "
            "{\"street\": null, \"city\": null, \"state\": null, \"zip_code\": null, \"country\": null}.\n\n"
            "Your response must ONLY contain valid JSON with no additional text before or after. "
            "Do not include explanations, do not use markdown formatting, just provide the JSON object.\n\n"
            "Here is the JSON schema to follow:\n" + self.schema_str
        )

        self.system_prompt = system_message

    def process_invoice_text(self, text: str) -> str:
        # Add debug flag
        debug = False # Set to False to disable debug output
        
        messages = [
            SystemMessage(content=self.system_prompt),
            HumanMessage(
                content=f"Invoice text:\n{text}\n\nExtract the information and format it according to the provided schema. Return ONLY the JSON object.")
        ]

        print(f"[InvoiceProcessor] Sending prompt with invoice text (length: {len(text)} chars)")
        print(f"[InvoiceProcessor] Full invoice text: {text}")

        # Debug: Write the full prompt to a file
        if debug:
            with open("llm_prompt.txt", "w", encoding="utf-8") as f:
                f.write("===== PROMPT SENT TO LLM =====\n")
                f.write(f"System: {self.system_prompt}\n\n")
                f.write(f"Human: Invoice text:\n{text}\n\nExtract the information and format it according to the provided schema. Return ONLY the JSON object.\n")
                f.write("===============================\n")

        llm_response = self.llm.invoke(messages)

        print(f"[InvoiceProcessor] Raw LLM response type: {type(llm_response)}")

        response_text = str(llm_response.content) if hasattr(llm_response, 'content') else str(llm_response)

        # Debug: Write the full response to a file
        if debug:
            with open("llm_response_debug.txt", "w", encoding="utf-8") as f:
                f.write("===== LLM RESPONSE =====\n")
                f.write(response_text)
                f.write("\n=========================\n")

        with open("llm_raw_response.txt", "w", encoding="utf-8") as f:
            f.write(response_text)

        print(f"[InvoiceProcessor] Response saved to llm_raw_response.txt")
        print(f"[InvoiceProcessor] Response preview: {response_text[:200]}...")

        try:
            parsed_json = self.output_parser.parse(response_text)
            return parsed_json
        except Exception as e:
            print(f"[InvoiceProcessor] Parser error: {str(e)}")
            raise
