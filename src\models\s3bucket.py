from io import StringIO
import os
import csv
import json
import time
import boto3
from typing import Union, List

from dotenv import load_dotenv

load_dotenv()

class FileLike:
    filename : str
    file_content : Union[str, bytes]

    def __init__(self, filename, file_content = bytes()):
        self.filename = filename
        self.file_content = file_content

    def read(self, bytes = -1) -> Union[str, bytes]:
        if bytes == -1:
            return self.file_content
        else:
            # split the content into bytes
            output = self.file_content[:bytes]
            self.file_content = self.file_content[bytes:]
            return output     

    def write(self, content : Union[str, bytes]):
        if type(content) == str:
            content = content.encode("utf-8")
        if type(self.file_content) == type(content):
            self.file_content += content
        else:
            raise TypeError(f"[FileLike] Cannot write {type(content)} to {type(self.file_content)}")

class S3BucketRepository:
    def __init__(self):
        access_key = os.getenv("AWS_ACCESS_KEY_ID")
        secret_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        if not access_key or not secret_key:
            raise ValueError("[S3BucketRepository] AWS credentials are not set in environment variables")
        self.s3 = boto3.client(
            "s3",
            aws_access_key_id=access_key,
            aws_secret_access_key=secret_key
        )
    
        self.upload_fileobj = self.s3.upload_fileobj
        self.download_fileobj = self.s3.download_fileobj
        self.list_objects_v2 = self.s3.list_objects_v2
        self.list_objects = self.s3.list_objects
    def read_all(self, bucket: str) -> List[FileLike]:
        self._fetch_or_create_bucket(bucket)
        # download all files from the bucket
        response = self.list_objects_v2(Bucket=bucket)
        files = []
        if 'Contents' in response:
            for obj in response['Contents']:
                filename = obj['Key']
                file = FileLike(filename)
                self.download_fileobj(bucket, filename, file)
                files.append(file)
        return files
    def read(self, bucket: str, filename_key: str) -> FileLike:
        self._fetch_or_create_bucket(bucket)
        # verify that the file exists
        response = self.list_objects_v2(Bucket=bucket, Prefix=filename_key)
        if 'Contents' not in response:
            raise FileNotFoundError(f"[S3BucketRepository] File {filename_key} not found in {bucket}")
        # download a specific file from the bucket
        file = FileLike(filename_key)
        self.download_fileobj(bucket, filename_key, file)
        return file
    def create(self, bucket: str, filename_key: str, file: FileLike) -> None:
        self._fetch_or_create_bucket(bucket)
        # upload a file to the bucket
        self.upload_fileobj(file, bucket, filename_key)
    def update(self, bucket: str, filename_key: str, file: FileLike) -> None:
        self.create(bucket, filename_key, file)
    def delete(self, bucket: str, filename_key: str) -> None:
        # if the bucket doesn't exist, issue a warning and ignore
        try:
            self.s3.delete_object(Bucket=bucket, Key=filename_key)
        except Exception as e:
            print(f"[S3BucketRepository] Error deleting {filename_key} from {bucket}: {e}")
    def _fetch_or_create_bucket(self, bucket: str) -> None:
        # check if the bucket exists
        response = self.list_objects_v2(Bucket=bucket)
        if 'Contents' not in response:
            # create the bucket if it does not exist
            self.s3.create_bucket(Bucket=bucket)
