from abc import ABC, abstractmethod
import boto3
import json
import time

class Queue<PERSON>oller(ABC):
    @abstractmethod
    def poll(self) -> list:
        pass

    @abstractmethod
    def poll_uptime(self) -> int:
        pass

    @abstractmethod
    def delete(self, message):
        pass

class SqsQueuePoller(QueuePoller):
    def __init__(self, queue_url):
        self.queue_url = queue_url
        self.sqs = boto3.client('sqs')
        self.poll_start_time = int(time.time())

    def poll(self):
        response = self.sqs.receive_message(
            QueueUrl=self.queue_url,
            MaxNumberOfMessages=1,
            WaitTimeSeconds=20
        )
        print(f"[SqsQueuePoller] Raw response: {response}")
        for message in response.get('Messages', []):
            if "Message" in json.loads(message["Body"]):
                message["Body"] = json.loads(json.loads(message["Body"])["Message"])
            else:
                print("[SqsQueuePoller] No S3 event found in message. Ignoring.")
        return response.get('Messages', [])

    def poll_uptime(self):
        return int(time.time()) - self.poll_start_time # time since creation of this poller instance

    def delete(self, message):
        self.sqs.delete_message(
            QueueUrl=self.queue_url,
            ReceiptHandle=message['ReceiptHandle']
        )