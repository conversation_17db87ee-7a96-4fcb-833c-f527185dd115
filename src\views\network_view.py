import streamlit as st
import plotly.graph_objects as go
import networkx as nx
import pandas as pd
from typing import Dict, List, Any


class NetworkView:
    """
    View component for displaying account relationships using a network graph.
    Follows the MVC pattern as a View component.
    """

    def __init__(self, processed_data: List[Dict[str, Any]], sub_account_colors: Dict[str, str]):
        """
        Initialize with processed invoice data and color mappings.

        Args:
            processed_data: List of processed invoice data dictionaries
            sub_account_colors: Mapping of sub-account names to hex color codes
        """
        self.processed_data = processed_data
        self.sub_account_colors = sub_account_colors

    def render(self):
        """Renders the network visualization in the Streamlit UI"""
        if not self.processed_data:
            st.info("No processed data available. Process invoices to view account relationships.")
            return

        # Build the graph
        G, positions, node_colors, node_sizes, edge_weights = self._build_graph()

        if not G.nodes():
            st.warning("No relationships found to visualize.")
            return

        # Create the network visualization
        edge_trace, node_trace = self._create_traces(G, positions, node_colors, node_sizes, edge_weights)

        # Create the figure
        fig = go.Figure(data=[edge_trace, node_trace],
                        layout=go.Layout(
                            title='Account Relationship Network',
                            showlegend=False,
                            hovermode='closest',
                            margin=dict(b=20, l=5, r=5, t=40),
                            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
                            width=800,
                            height=800
                        ))

        # Display the chart
        st.plotly_chart(fig, use_container_width=True)

        # Show network metrics
        self._display_metrics(G)

    def _build_graph(self):
        """Builds a NetworkX graph from the invoice data"""
        G = nx.Graph()

        # Add central "Expenses" node
        G.add_node("Expenses", type="root")

        # Track suppliers and sub-accounts
        suppliers = {}
        sub_accounts = {}

        # Process each invoice
        for data in self.processed_data:
            if 'parsed_data' not in data:
                continue

            parsed_data = data['parsed_data']

            # Get supplier
            supplier_name = (parsed_data.get('invoice_header', {}).get('supplier', {}) or {}).get('name', 'Unknown Supplier')
            if supplier_name not in suppliers:
                suppliers[supplier_name] = 0

            # Process line items
            if 'line_items' in parsed_data:
                for item in parsed_data['line_items']:
                    amount = float(item.get('total_price', 0))
                    sub_account = item.get('sub_account', 'Uncategorized')

                    # Update supplier total
                    suppliers[supplier_name] += amount

                    # Update sub-account total
                    if sub_account not in sub_accounts:
                        sub_accounts[sub_account] = 0
                    sub_accounts[sub_account] += amount

                    # Add or update nodes and edges
                    if not G.has_node(supplier_name):
                        G.add_node(supplier_name, type="supplier")

                    if not G.has_node(sub_account):
                        G.add_node(sub_account, type="sub_account")

                    # Connect supplier to sub-account
                    if G.has_edge(supplier_name, sub_account):
                        G[supplier_name][sub_account]['weight'] += amount
                    else:
                        G.add_edge(supplier_name, sub_account, weight=amount)

                    # Connect sub-account to central Expenses node
                    if G.has_edge(sub_account, "Expenses"):
                        G[sub_account]["Expenses"]['weight'] += amount
                    else:
                        G.add_edge(sub_account, "Expenses", weight=amount)

        # Calculate layout
        positions = nx.spring_layout(G, seed=42)

        # Prepare node colors and sizes
        node_colors = []
        node_sizes = []

        max_supplier_amount = max(suppliers.values()) if suppliers else 1
        max_sub_account_amount = max(sub_accounts.values()) if sub_accounts else 1

        for node in G.nodes():
            if node == "Expenses":
                node_colors.append("#1E88E5")  # Blue for central node
                node_sizes.append(30)  # Fixed size for central node
            elif node in suppliers:
                node_colors.append("#FFC107")  # Yellow for suppliers
                amount = suppliers[node]
                node_sizes.append(20 * (amount / max_supplier_amount) + 10)
            else:  # sub-account
                node_colors.append(self.sub_account_colors.get(node, "#CCCCCC"))
                amount = sub_accounts.get(node, 0)
                node_sizes.append(20 * (amount / max_sub_account_amount) + 10)

        # Prepare edge weights
        all_weights = [G[u][v]['weight'] for u, v in G.edges()]
        max_weight = max(all_weights) if all_weights else 1
        edge_weights = [2 * (weight / max_weight) + 1 for weight in all_weights]

        return G, positions, node_colors, node_sizes, edge_weights

    def _create_traces(self, G, positions, node_colors, node_sizes, edge_weights):
        """Creates the Plotly traces for edges and nodes"""
        # Create edges trace
        edge_x = []
        edge_y = []
        edge_trace_widths = []

        for i, (u, v) in enumerate(G.edges()):
            x0, y0 = positions[u]
            x1, y1 = positions[v]
            edge_x.extend([x0, x1, None])
            edge_y.extend([y0, y1, None])
            edge_trace_widths.extend([edge_weights[i], edge_weights[i], None])

        edge_trace = go.Scatter(
            x=edge_x, y=edge_y,
            line=dict(width=1, color='#888'),
            hoverinfo='none',
            mode='lines',
            showlegend=False
        )

        # Create nodes trace
        node_x = []
        node_y = []
        node_text = []

        for node in G.nodes():
            x, y = positions[node]
            node_x.append(x)
            node_y.append(y)

            # Node text for hovering
            if node == "Expenses":
                node_text.append("All Expenses")
            elif node in G.nodes():
                connected_nodes = list(G.neighbors(node))
                node_text.append(f"{node}<br>Connected to {len(connected_nodes)} nodes")

        node_trace = go.Scatter(
            x=node_x, y=node_y,
            mode='markers+text',
            text=[node if node == "Expenses" else "" for node in G.nodes()],  # Only show text for central node
            textposition="middle center",
            hovertext=node_text,
            hoverinfo='text',
            marker=dict(
                showscale=False,
                color=node_colors,
                size=node_sizes,
                line=dict(width=1, color='#888')
            )
        )

        return edge_trace, node_trace

    def _display_metrics(self, G):
        """Displays network metrics in the UI"""
        col1, col2, col3 = st.columns(3)

        with col1:
            st.metric("Total Nodes", len(G.nodes()))

        with col2:
            st.metric("Total Connections", len(G.edges()))

        with col3:
            density = nx.density(G)
            st.metric("Network Density", f"{density:.4f}")

        with st.expander("Network Explanation"):
            st.markdown("""
            This network visualization shows the relationships between:

            - **Suppliers** (yellow nodes)
            - **Expense Categories** (colored nodes)
            - **Total Expenses** (blue central node)

            The size of each node represents its total amount, and the thickness of connections
            represents the strength of the relationship (total amount between connected entities).

            Network density measures how interconnected the network is (0-1 scale).
            """)