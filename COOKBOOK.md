# Design Patterns Cookbook for PiKernel

This cookbook provides practical guidance on how to leverage design patterns in the PiKernel Invoice Processor. Each section describes a pattern, shows its implementation in the app, and provides examples for extending functionality.

## Table of Contents

1. [Factory Pattern](#factory-pattern)
2. [Strategy Pattern](#strategy-pattern)
3. [Chain of Responsibility Pattern](#chain-of-responsibility-pattern)
4. [Adapter Pattern](#adapter-pattern)
5. [<PERSON>acade Pattern](#facade-pattern)
6. [Template Method Pattern](#template-method-pattern)

## Factory Pattern

### Description
The Factory pattern creates objects without specifying the exact class to create. It encapsulates object creation logic, allowing for flexibility in what objects are created and how they're created.

### Implementation in PiKernel
The app uses factories for creating Chart of Accounts and LLM clients:

```python
# ChartOfAccounts Factory
@staticmethod
def create(coa_type: str = "Excel", file_path: str = None) -> 'ChartOfAccounts':
    if coa_type == "Excel":
        return ExcelChartOfAccounts(file_path)
    else:
        raise ValueError(f"Invalid CoA type: {coa_type}")

# LLM Factory
class LLMFactory:
    def __init__(self):
        self.providers = {
            "openai": OpenAIProvider(),
            "anthropic": AnthropicProvider(),
            "google": GoogleProvider(),
        }

    def create_llm(self, provider, model_name, **kwargs):
        provider_lower = provider.lower()
        if provider_lower not in self.providers:
            raise Exception(f"Unsupported provider: {provider}")
        return self.providers[provider_lower].create_llm(model_name, **kwargs)
```

### Extension Example: Adding a New LLM Provider

Let's add support for a new LLM provider, Mistral:

1. Create a new provider class:

```python
# Add to src/models/llm_factory.py

from langchain_mistralai import ChatMistralAI

class MistralProvider:
    def create_llm(self, model_name, **kwargs):
        return ChatMistralAI(model=model_name, temperature=0, **kwargs)
```

2. Register the provider in the factory:

```python
class LLMFactory:
    def __init__(self):
        self.providers = {
            "openai": OpenAIProvider(),
            "anthropic": AnthropicProvider(),
            "google": GoogleProvider(),
            "mistral": MistralProvider(),  # Add the new provider
        }
```

3. Update the sidebar UI to include the new option:

```python
# In src/app/sidebar.py

def render_sidebar():
    # ... existing code ...
    
    provider = st.sidebar.selectbox(
        "Select LLM Provider",
        ["OpenAI", "Anthropic", "Google", "Mistral"],  # Add Mistral
        index=0
    )
    
    # Add model selection for Mistral
    if provider == "Mistral":
        model_name = st.sidebar.selectbox(
            "Select Model",
            ["mistral-tiny", "mistral-small", "mistral-medium", "mistral-large"]
        )
    # ... rest of the function ...
```

## Strategy Pattern

### Description
The Strategy pattern defines a family of algorithms, encapsulates each one, and makes them interchangeable. It enables selecting an algorithm at runtime without altering client code.

### Implementation in PiKernel
The app uses the Strategy pattern for OCR and invoice classification:

```python
# OCR strategies
class GoogleVisionOCR:
    def extract_text(self, file_path: str) -> OCRResult:
        # Google-specific implementation...

class AzureOCR:
    def extract_text(self, file_path: str) -> OCRResult:
        # Azure-specific implementation...

# Invoice classification strategies
class InvoiceClassifier(ABC):
    @abstractmethod
    def classify(self, text: str) -> str:
        pass

class WordCountClassifier(InvoiceClassifier):
    def classify(self, text: str) -> str:
        word_count = len(text.split())
        if word_count > self.threshold:
            return "COMPLEX"
        else:
            return "REGULAR"
```

### Extension Example: Adding a New Classifier Strategy

Let's create a classifier based on keyword detection:

1. Create a new classifier class:

```python
# Add to src/models/classifiers.py

class KeywordClassifier(InvoiceClassifier):
    """Classifies invoices based on presence of keywords"""
    
    def __init__(self, alphagrep_keywords=None, regular_keywords=None):
        self.alphagrep_keywords = alphagrep_keywords or [
            "complex", "multi-page", "enterprise", "advanced"
        ]
        self.regular_keywords = regular_keywords or [
            "simple", "basic", "standard", "regular"
        ]
    
    def classify(self, text: str) -> str:
        text_lower = text.lower()
        
        # Count matches for each category
        alphagrep_count = sum(1 for word in self.alphagrep_keywords if word.lower() in text_lower)
        regular_count = sum(1 for word in self.regular_keywords if word.lower() in text_lower)
        
        print(f"[KeywordClassifier] ALPHAGREP matches: {alphagrep_count}, REGULAR matches: {regular_count}")
        
        # Classify based on which has more matches
        if alphagrep_count > regular_count:
            return "ALPHAGREP"
        elif regular_count > alphagrep_count:
            return "REGULAR"
        else:
            # If tied or no matches, fall back to word count
            return WordCountClassifier().classify(text)
```

2. Update the factory to support the new classifier:

```python
# Update src/models/classifier_factory.py

@staticmethod
def create_classifier(classifier_type: str = "word_count", **kwargs) -> InvoiceClassifier:
    """Create a classifier based on type"""
    if classifier_type == "word_count":
        threshold = kwargs.get("threshold", 12000)
        return WordCountClassifier(threshold=threshold)
    elif classifier_type == "keyword":  # Add new classifier type
        alphagrep_keywords = kwargs.get("alphagrep_keywords")
        regular_keywords = kwargs.get("regular_keywords")
        return KeywordClassifier(
            alphagrep_keywords=alphagrep_keywords,
            regular_keywords=regular_keywords
        )
    else:
        raise ValueError(f"Unsupported classifier type: {classifier_type}")
```

3. Use the new classifier in the invoice_type_classifier_node:

```python
# In src/state/nodes.py, modify invoice_type_classifier_node

def invoice_type_classifier_node(state: dict) -> dict:
    """Classifies the invoice based on Google OCR text"""
    google_ocr_text = state.get("ocr_results", {}).get("google", "")

    if not google_ocr_text:
        print("[invoice_type_classifier_node] No Google OCR text available for classification.")
        state["invoice_type"] = "UNKNOWN"
        return state

    # Use the new keyword classifier
    classifier = ClassifierFactory.create_classifier(
        "keyword",
        alphagrep_keywords=["invoice", "subtotal", "tax", "total", "payment"],
        regular_keywords=["receipt", "simple", "basic"]
    )

    # Classify the invoice
    invoice_type = classifier.classify(google_ocr_text)
    state["invoice_type"] = invoice_type
    print(f"[invoice_type_classifier_node] Invoice classified as: {invoice_type}")

    return state
```

## Chain of Responsibility Pattern

### Description
The Chain of Responsibility pattern creates a chain of receiver objects for a request. Each receiver contains a reference to the next receiver, and the request propagates down the chain until it's handled.

### Implementation in PiKernel
The app uses a graph structure to implement a Chain of Responsibility for invoice processing:

```python
# In src/controllers/invoice_controller.py

# Add nodes
graph_wrapper.add_node("copy", copy_file_node)
graph_wrapper.add_node("google_ocr", google_vision_ocr_node)
graph_wrapper.add_node("invoice_type", invoice_type_classifier_node)
# ... more nodes ...

# Add fixed edges (defines the chain)
graph_wrapper.add_edge(START, "copy")
graph_wrapper.add_edge("copy", "google_ocr")
graph_wrapper.add_edge("google_ocr", "invoice_type")
# ... more edges ...

# Add conditional edge with router function
graph_wrapper.add_conditional_edge("invoice_type", route_by_invoice_type)
```

### Extension Example: Adding a New Processing Node

Let's add a node for detecting duplicates:

1. Create a new node function:

```python
# Add to src/state/nodes.py

def duplicate_detection_node(state: dict) -> dict:
    """
    Checks if the current invoice is a duplicate of previously processed ones.
    Compares invoice number and supplier information.
    """
    print("[duplicate_detection_node] Checking for duplicate invoices")
    
    # Extract current invoice details
    parsed_data = state.get("parsed_data", {})
    invoice_header = parsed_data.get("invoice_header", {})
    invoice_number = invoice_header.get("invoice_number")
    supplier_name = invoice_header.get("supplier", {}).get("name")
    
    if not invoice_number or not supplier_name:
        print("[duplicate_detection_node] Missing invoice number or supplier name, cannot check for duplicates")
        state["is_duplicate"] = False
        return state
    
    # Create a simple fingerprint
    fingerprint = f"{supplier_name}_{invoice_number}"
    
    # Get previously processed fingerprints
    if "processed_fingerprints" not in state:
        state["processed_fingerprints"] = set()
    
    # Check if this is a duplicate
    if fingerprint in state["processed_fingerprints"]:
        print(f"[duplicate_detection_node] Duplicate detected: {fingerprint}")
        state["is_duplicate"] = True
    else:
        print(f"[duplicate_detection_node] New invoice: {fingerprint}")
        state["processed_fingerprints"].add(fingerprint)
        state["is_duplicate"] = False
    
    return state
```

2. Create a router function to handle duplicates:

```python
# Add to src/state/nodes.py or appropriate location

def route_by_duplicate_status(state: dict) -> str:
    """Route based on duplicate status"""
    is_duplicate = state.get("is_duplicate", False)
    
    if is_duplicate:
        print(f"[route_by_duplicate_status] Duplicate invoice detected, skipping further processing")
        return "skip_processing"
    else:
        print(f"[route_by_duplicate_status] New invoice, continuing to regular processing")
        return "intent"  # Continue to intent node
```

3. Update the graph to include the new node and conditional routing:

```python
# In the graph construction code (e.g., in src/controllers/invoice_controller.py)

# Add the new node
graph_wrapper.add_node("duplicate_check", duplicate_detection_node)
graph_wrapper.add_node("skip_processing", lambda state: state)  # Dummy node for skipped processing

# Update the edges
graph_wrapper.add_edge("llm_parser", "duplicate_check")  # Add after parsing
graph_wrapper.add_conditional_edge("duplicate_check", route_by_duplicate_status)  # Add conditional routing

# Connect the skip_processing node to the end
graph_wrapper.add_edge("skip_processing", END)

# Regular path continues from intent
graph_wrapper.add_edge("intent", "frequency")
# ... rest of the edges ...
```

## Adapter Pattern

### Description
The Adapter pattern converts the interface of a class into another interface clients expect. It allows classes to work together that couldn't otherwise due to incompatible interfaces.

### Implementation in PiKernel
The app uses adapters for different LLM providers:

```python
class OpenAIProvider:
    def create_llm(self, model_name, **kwargs):
        return ChatOpenAI(model_name=model_name, temperature=0, **kwargs)

class AnthropicProvider:
    def create_llm(self, model_name, **kwargs):
        return ChatAnthropic(model=model_name, temperature=0, **kwargs)

class GoogleProvider:
    def create_llm(self, model_name, **kwargs):
        return ChatGoogleGenerativeAI(model_name=model_name, temperature=0, **kwargs)
```

### Extension Example: Adding a Local LLM Adapter

Let's add support for running local LLMs via the Ollama project:

1. Create a new adapter class:

```python
# Add to src/models/llm_factory.py

from langchain_community.llms import Ollama

class OllamaProvider:
    def create_llm(self, model_name, **kwargs):
        """
        Adapter to use local Ollama models with the same interface
        as cloud LLM providers.
        """
        # Set default endpoint if not provided
        endpoint = kwargs.pop("base_url", "http://localhost:11434")
        
        # Create a chat template to make Ollama work like chat models
        prompt_template = """
        <s>[INST] <<SYS>>
        {system}
        <</SYS>>
        
        {human} [/INST]
        """
        
        # Return Ollama LLM with formatting
        return Ollama(
            model=model_name,
            base_url=endpoint,
            temperature=0,
            format="json",  # Request JSON format
            prompt_template=prompt_template,
            **kwargs
        )
```

2. Register the provider in the factory:

```python
class LLMFactory:
    def __init__(self):
        self.providers = {
            "openai": OpenAIProvider(),
            "anthropic": AnthropicProvider(),
            "google": GoogleProvider(),
            "ollama": OllamaProvider(),  # Add the local LLM provider
        }
```

3. Update the sidebar UI to include the new option:

```python
# In src/app/sidebar.py

def render_sidebar():
    # ... existing code ...
    
    provider = st.sidebar.selectbox(
        "Select LLM Provider",
        ["OpenAI", "Anthropic", "Google", "Ollama"],  # Add Ollama
        index=0
    )
    
    # Add model selection for Ollama
    if provider == "Ollama":
        model_name = st.sidebar.selectbox(
            "Select Model",
            ["llama3", "llama3:8b", "mistral", "mixtral", "phi"]
        )
        # Add Ollama-specific configuration
        ollama_endpoint = st.sidebar.text_input(
            "Ollama Endpoint",
            value="http://localhost:11434"
        )
        st.session_state.ollama_endpoint = ollama_endpoint
    # ... rest of the function ...
```

4. Update LLM creation to pass provider-specific parameters:

```python
# In src/state/nodes.py, modify llm_parser_node

def llm_parser_node(state: dict) -> dict:
    # ... existing code ...
    
    llm_provider = state.get("llm_provider", "openai")
    llm_model = state.get("llm_model", "gpt-4o")
    
    # Provider-specific parameters
    provider_params = {}
    if llm_provider.lower() == "ollama":
        provider_params["base_url"] = st.session_state.get("ollama_endpoint", "http://localhost:11434")
    
    # Create LLM with provider-specific parameters
    llm = factory.create_llm(llm_provider, model_name=llm_model, **provider_params)
    
    # ... rest of the function ...
```

## Facade Pattern

### Description
The Facade pattern provides a unified interface to a set of interfaces in a subsystem. It defines a higher-level interface that makes the subsystem easier to use.

### Implementation in PiKernel
The `InvoiceController` class serves as a facade to the complex processing pipeline:

```python
class InvoiceController:
    def process_invoices(self, files: List[Any], provider: str, model_name: str):
        """
        Process uploaded invoice files using the specified LLM provider and model.
        Hides the complexity of the entire processing pipeline.
        """
        results = {}
        for uploaded_file in files:
            # Create temporary file
            # Set up state
            # Create graph
            # Add nodes
            # Add edges
            # Run the graph
            # Store results
        return results
```

### Extension Example: Adding a Batch Processing Facade

Let's create a facade for batch processing of invoices from a folder:

```python
# Add to src/controllers/batch_controller.py

import os
import glob
from typing import List, Dict, Any
from src.controllers.invoice_controller import InvoiceController

class BatchProcessingFacade:
    """
    Facade for batch processing of invoices from folders.
    Simplifies bulk invoice processing operations.
    """
    
    def __init__(self, base_input_dir: str, output_dir: str):
        """
        Initialize with base input and output directories.
        
        Args:
            base_input_dir: Base directory containing input folders
            output_dir: Directory for output files
        """
        self.base_input_dir = base_input_dir
        self.output_dir = output_dir
        self.controller = InvoiceController(base_input_dir, output_dir)
    
    def process_folder(self, folder_name: str, provider: str, model_name: str) -> Dict[str, Any]:
        """
        Process all invoices in a specific folder.
        
        Args:
            folder_name: Name of subfolder in base_input_dir
            provider: LLM provider to use
            model_name: LLM model to use
            
        Returns:
            Dictionary of results
        """
        folder_path = os.path.join(self.base_input_dir, folder_name)
        
        # Create output subfolder
        output_subfolder = os.path.join(self.output_dir, folder_name)
        os.makedirs(output_subfolder, exist_ok=True)
        
        # Find all PDFs in the folder
        pdf_files = glob.glob(os.path.join(folder_path, "*.pdf"))
        
        print(f"[BatchProcessingFacade] Processing {len(pdf_files)} files from {folder_path}")
        
        # Create a controller for this specific folder
        folder_controller = InvoiceController(folder_path, output_subfolder)
        
        # Process all files in the folder
        results = {}
        for pdf_file in pdf_files:
            file_name = os.path.basename(pdf_file)
            try:
                # Load file content
                with open(pdf_file, "rb") as f:
                    file_content = f.read()
                
                # Create a simple file-like object for the controller
                class SimpleUploadedFile:
                    def __init__(self, name, content):
                        self.name = name
                        self._content = content
                    
                    def read(self):
                        return self._content
                
                uploaded_file = SimpleUploadedFile(file_name, file_content)
                
                # Process the file
                file_results = folder_controller.process_invoices([uploaded_file], provider, model_name)
                
                # Store results
                results.update(file_results)
                
            except Exception as e:
                print(f"[BatchProcessingFacade] Error processing {file_name}: {e}")
                results[file_name] = {"error": str(e)}
        
        return results
    
    def process_all_folders(self, provider: str, model_name: str) -> Dict[str, Dict[str, Any]]:
        """
        Process all folders in the base input directory.
        
        Args:
            provider: LLM provider to use
            model_name: LLM model to use
            
        Returns:
            Dictionary of results by folder
        """
        # Get all subdirectories
        folders = [f for f in os.listdir(self.base_input_dir) 
                  if os.path.isdir(os.path.join(self.base_input_dir, f))]
        
        # Process each folder
        results = {}
        for folder in folders:
            folder_results = self.process_folder(folder, provider, model_name)
            results[folder] = folder_results
        
        return results
```

Using the facade:

```python
# Example usage in a script or command-line tool

def process_batch(input_base_dir, output_dir, provider, model):
    """Process a batch of invoices from multiple folders"""
    
    facade = BatchProcessingFacade(input_base_dir, output_dir)
    
    # Process specific folder
    results = facade.process_folder("2025-03-invoices", provider, model)
    print(f"Processed folder results: {len(results)} files")
    
    # Or process all folders
    # all_results = facade.process_all_folders(provider, model)
    # print(f"Processed all folders: {len(all_results)} folders")
    
    return results
```

## Template Method Pattern

### Description
The Template Method pattern defines the skeleton of an algorithm in a method, deferring some steps to subclasses. It lets subclasses redefine certain steps of an algorithm without changing the algorithm's structure.

### Implementation in PiKernel
The `ChartOfAccounts` abstract base class defines the template method `generate_colors()`:

```python
class ChartOfAccounts(ABC):
    """Abstract base class for Chart of Accounts functionality."""

    @abstractmethod
    def get_unique_subtypes(self) -> Set[str]:
        """Returns a set of unique sub-account types"""
        pass

    @abstractmethod
    def get_account_mapping(self) -> Dict[str, str]:
        """Returns a dictionary mapping account names to their sub-account types"""
        pass

    def generate_colors(self) -> Dict[str, str]:
        """
        Template method that generates colors for sub-account types.
        Uses abstract methods that subclasses must implement.
        """
        unique_subtypes = list(self.get_unique_subtypes())
        n = len(unique_subtypes)
        hsv_tuples = [(x * 1.0 / n, 0.5, 0.9) for x in range(n)]
        rgb_tuples = [colorsys.hsv_to_rgb(*x) for x in hsv_tuples]
        color_dict = {}
        for subtype, (r, g, b) in zip(unique_subtypes, rgb_tuples):
            color_dict[subtype] = '#{:02x}{:02x}{:02x}'.format(
                int(r * 255), int(g * 255), int(b * 255))
        return color_dict
```

### Extension Example: Creating a New Chart of Accounts Implementation

Let's create a Chart of Accounts that loads from a CSV file:

```python
# Add to src/models/chart_of_accounts.py

class CSVChartOfAccounts(ChartOfAccounts):
    """
    Implementation of Chart of Accounts that loads data from a CSV file.
    """

    def __init__(self, file_path: str = None):
        """
        Initialize with path to CSV file containing Chart of Accounts data.

        Args:
            file_path (str, optional): Path to the CSV file. If None, defaults to 'CoA.csv'
                                      in the project's assets directory.
        """
        if file_path is None:
            # Default to the assets directory in the project
            base_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'assets')
            file_path = os.path.join(base_path, 'CoA.csv')

        print(f"[CSVChartOfAccounts] Loading Chart of Accounts from {file_path}")

        try:
            import csv
            with open(file_path, 'r', newline='') as csvfile:
                reader = csv.DictReader(csvfile)
                # Convert to a list of dictionaries
                rows = list(reader)
            
            # Convert the list to a DataFrame for consistency with ExcelChartOfAccounts
            self.df = pd.DataFrame(rows)
            print(f"[CSVChartOfAccounts] Successfully loaded CSV file with {len(self.df)} rows")
        except Exception as e:
            print(f"[CSVChartOfAccounts] Error loading CSV file: {e}")
            # Create an empty DataFrame with expected columns
            self.df = pd.DataFrame(columns=['Account type', 'Account subtype', 'Full name'])

        # Use the same processing as ExcelChartOfAccounts
        self.expense_accounts = self._process_expense_accounts()
        self.unique_subtypes = self._get_unique_subtypes()
        self.account_mapping = self._create_account_mapping()

    def _process_expense_accounts(self) -> pd.DataFrame:
        """Filter accounts to only include expense accounts"""
        expense_df = self.df[self.df['Account type'] == 'Expenses'].copy()
        print(f"[CSVChartOfAccounts] Found {len(expense_df)} expense accounts")
        return expense_df

    def _get_unique_subtypes(self) -> Set[str]:
        """Get unique sub-account types from expense accounts"""
        subtypes = set(self.expense_accounts['Account subtype'].unique())
        print(f"[CSVChartOfAccounts] Found {len(subtypes)} unique subtypes")
        return subtypes

    def _create_account_mapping(self) -> Dict[str, str]:
        """Create mapping from full account names to their sub-account types"""
        mapping = dict(zip(self.expense_accounts['Full name'], self.expense_accounts['Account subtype']))
        print(f"[CSVChartOfAccounts] Created mapping for {len(mapping)} accounts")
        return mapping

    def get_unique_subtypes(self) -> Set[str]:
        """Implementation of abstract method to get unique subtypes"""
        return self.unique_subtypes

    def get_account_mapping(self) -> Dict[str, str]:
        """Implementation of abstract method to get account mapping"""
        return self.account_mapping
```

Then update the factory method to support this new implementation:

```python
@staticmethod
def create(coa_type: str = "Excel", file_path: str = None) -> 'ChartOfAccounts':
    """
    Factory method to create appropriate Chart of Accounts implementation.

    Args:
        coa_type (str): Type of Chart of Accounts implementation to create
        file_path (str, optional): Path to the CoA file

    Returns:
        ChartOfAccounts: An implementation of the ChartOfAccounts class

    Raises:
        ValueError: If an invalid CoA type is specified
    """
    if coa_type == "Excel":
        return ExcelChartOfAccounts(file_path)
    elif coa_type == "CSV":  # Add support for CSV
        return CSVChartOfAccounts(file_path)
    else:
        raise ValueError(f"Invalid CoA type: {coa_type}")
```

Now you can use a CSV file for the Chart of Accounts:

```python
# In the application code
chart_of_accounts = ChartOfAccounts.create("CSV", "path/to/your/chart_of_accounts.csv")
```

## Conclusion

By leveraging these design patterns, the PiKernel Invoice Processor becomes highly extensible. You can:

1. Add new LLM providers using the Factory and Adapter patterns
2. Create new invoice classifiers with the Strategy pattern
3. Extend the processing pipeline with the Chain of Responsibility pattern
4. Load from different data sources using the Template Method pattern
5. Simplify complex operations with the Facade pattern

When extending the application, follow the existing patterns to maintain consistency and ensure your additions integrate seamlessly with the current architecture.